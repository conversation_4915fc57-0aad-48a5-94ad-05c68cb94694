package com.android.dynamiclibrary.ui.topic.ui.topicdetail

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.math.MathUtils
import androidx.core.view.ViewCompat
import com.android.dynamiclibrary.R
import com.androidtool.common.extension.dp
import com.androidtool.common.utils.ScreenUtil
import kotlin.math.max
import kotlin.math.min

/**
 * 自定义 Behavior 用于处理头部区域的滑动效果
 * 实现当用户向上滑动时，头部区域逐渐折叠，只保留 title_bar 可见
 */
class ProfileHeaderBehavior(context: Context, attrs: AttributeSet?) :
    CoordinatorLayout.Behavior<View>(context, attrs) {

    /**
     * 这里的 56.dp 作为标题栏的最小高度，再加上状态栏的高度，
     * 以便在 Edge-to-Edge 模式下也保证标题栏始终可见。
     */
    private val minHeight: Int by lazy {
        56.dp + getStatusBarHeight() + 20.dp
    }

    // 记录原始高度，用于计算折叠比例
    private var originalHeight = -1

    override fun onLayoutChild(
        parent: CoordinatorLayout,
        child: View,
        layoutDirection: Int
    ): Boolean {
        // 只在初次布局时记录原始高度
        if (originalHeight == -1) {
            originalHeight = child.measuredHeight
        }
        return super.onLayoutChild(parent, child, layoutDirection)
    }

    override fun onStartNestedScroll(
        coordinatorLayout: CoordinatorLayout,
        child: View,
        directTargetChild: View,
        target: View,
        axes: Int,
        type: Int
    ): Boolean {
        // 只响应垂直方向的滑动
        return axes == ViewCompat.SCROLL_AXIS_VERTICAL
    }

    override fun onNestedScroll(
        coordinatorLayout: CoordinatorLayout,
        child: View,
        target: View,
        dxConsumed: Int,
        dyConsumed: Int,
        dxUnconsumed: Int,
        dyUnconsumed: Int,
        type: Int,
        consumed: IntArray
    ) {
        super.onNestedScroll(
            coordinatorLayout, child, target, dxConsumed, dyConsumed,
            dxUnconsumed, dyUnconsumed, type, consumed
        )

        // 计算新的高度，确保不小于 minHeight
        val newHeight = MathUtils.clamp(
            child.height - dyConsumed,
            minHeight,
            originalHeight
        )
        child.layoutParams.height = newHeight
        child.requestLayout()

        // 更新子视图（头像、标题栏等）的可见性/透明度
        updateChildViews(child, newHeight)
    }

    override fun onNestedPreScroll(
        coordinatorLayout: CoordinatorLayout,
        child: View,
        target: View,
        dx: Int,
        dy: Int,
        consumed: IntArray,
        type: Int
    ) {
        // 向上滑动：若当前高度大于 minHeight，则优先消耗一部分滑动
        if (dy > 0 && child.height > minHeight) {
            val consumedY = min(dy, child.height - minHeight)
            consumed[1] = consumedY

            val finalHeight = child.height - consumedY
            child.layoutParams.height = finalHeight
            child.requestLayout()

            updateChildViews(child, finalHeight)
        }
        // 向下滑动：若头部未完全展开并且 target 已无法继续向下滚动
        else if (dy < 0 && child.height < originalHeight && !target.canScrollVertically(-1)) {
            // 防止越界，最多展开至 originalHeight
            val consumedY = max(dy, minHeight - originalHeight)
            consumed[1] = consumedY

            val finalHeight = child.height - consumedY
            child.layoutParams.height = MathUtils.clamp(finalHeight, minHeight, originalHeight)
            child.requestLayout()

            updateChildViews(child, child.layoutParams.height)
        }
    }

    /**
     * 根据新的 Height 更新各种子视图（个人资料区、标题栏、左右图标等）的可见性及透明度。
     */
    private fun updateChildViews(parentView: View, currentHeight: Int) {
        val profileContent = parentView.findViewById<View>(R.id.profile_content)
        val titleBar = parentView.findViewById<View>(R.id.title_bar)

        // 标题栏中间文字和左右图标
        val tvTitleBar = titleBar.findViewById<View>(R.id.tv_title_bar)
        val ivBack = titleBar.findViewById<View>(R.id.iv_back)
        val ivShare = titleBar.findViewById<View>(R.id.iv_share)

        // 折叠比例: 0f => 完全展开, 1f => 完全折叠
        val collapsedFraction = (originalHeight - currentHeight).toFloat() /
                (originalHeight - minHeight)

        // 个人资料（头像+签名）在折叠到一定比例后渐隐
        if (collapsedFraction > 0.8f) {
            profileContent.alpha = (1 - collapsedFraction) * 5f
            if (collapsedFraction >= 1f) {
                profileContent.visibility = View.GONE
            } else {
                profileContent.visibility = View.VISIBLE
            }
        } else {
            profileContent.alpha = 1f
            profileContent.visibility = View.VISIBLE
        }

        // 标题栏中间文字在折叠时出现，不折叠时隐藏
        tvTitleBar.alpha = collapsedFraction

        // 左右图标一直可见
        ivBack.alpha = 1f
        ivShare.alpha = 1f
    }

    private fun getStatusBarHeight(): Int {
        return ScreenUtil.getStatusBarHeight()
    }
}
