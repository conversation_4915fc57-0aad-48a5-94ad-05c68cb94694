package com.android.dynamiclibrary.ui.dynamic.detail.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.android.dynamiclibrary.bean.vo.GiftRankListBean
import com.android.dynamiclibrary.ui.topic.repo.TopicRepository
import com.androidtool.common.base.event.LoadState
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch

class GiftListViewModel : ViewModel() {
    private val repository = TopicRepository.getSingleInstance()
    private var currentPage = 1
    private var currentId = ""

    private val _uiState =
        MutableStateFlow<GiftListUiState<GiftRankListBean.GiftRankItem>>(GiftListUiState.Loading)
    val giftRankListUiState = _uiState.asStateFlow()

    private val _uiEvent = MutableSharedFlow<GiftListUiEvent>()
    val giftRankListUiEvent = _uiEvent.asSharedFlow()

    /**
     * 初始化数据加载
     * @param id 数据ID
     */
    fun initDataById(id: String) {
        if (currentId != id) {
            this.currentId = id
            this.currentPage = 1
        }
        loadData(isRefresh = true)
    }

    /**
     * 刷新数据
     */
    fun refresh() {
        currentPage = 1
        loadData(isRefresh = true)
    }

    /**
     * 加载更多数据
     */
    fun loadMore() {
        if (_uiState.value is GiftListUiState.Success) {
            val currentState =
                _uiState.value as GiftListUiState.Success<GiftRankListBean.GiftRankItem>
            // 只有在非加载中且非加载完成状态才能触发加载更多
            if (currentState.loadState != LoadState.LOADING_MORE &&
                currentState.loadState != LoadState.LOAD_MORE_END
            ) {
                loadData(isRefresh = false)
            }
        }
    }

    /**
     * 加载数据
     * @param isRefresh 是否为刷新操作
     */
    private fun loadData(isRefresh: Boolean = false) {
        viewModelScope.launch {
            updateLoadingState(isRefresh)

            repository.loadGiftRankListById(currentId, currentPage)
                .catch { exception ->
                    handleError(exception, isRefresh)
                }
                .collect { result ->
                    result.fold(
                        onSuccess = { data ->
                            handleSuccess(
                                data.count,
                                data.list,
                                isRefresh
                            )
                        },
                        onFailure = { exception -> handleError(exception, isRefresh) }
                    )
                }
        }
    }

    /**
     * 更新加载状态
     */
    private fun updateLoadingState(isRefresh: Boolean) {
        if (isRefresh) {
            _uiState.value = if (_uiState.value is GiftListUiState.Success) {
                (_uiState.value as GiftListUiState.Success<GiftRankListBean.GiftRankItem>).copy(
                    loadState = LoadState.REFRESHING
                )
            } else {
                GiftListUiState.Loading
            }
        } else if (_uiState.value is GiftListUiState.Success) {
            _uiState.value =
                (_uiState.value as GiftListUiState.Success<GiftRankListBean.GiftRankItem>).copy(
                    loadState = LoadState.LOADING_MORE
                )
        }
    }

    /**
     * 处理成功加载的数据
     */
    private suspend fun handleSuccess(
        count: Int,
        data: List<GiftRankListBean.GiftRankItem>,
        isRefresh: Boolean,
    ) {
        if (isRefresh) {
            // 处理空数据情况
            if (data.isEmpty()) {
                _uiState.value = GiftListUiState.Empty
                _uiEvent.emit(GiftListUiEvent.UpdatePeopleCount(0))
                return
            }

            _uiState.value = GiftListUiState.Success(
                items = data,
                loadState = if (data.size < TopicRepository.PAGE_SIZE) LoadState.LOAD_MORE_END else LoadState.LOAD_MORE_COMPLETE,
                page = currentPage,
            )
            _uiEvent.emit(GiftListUiEvent.UpdatePeopleCount(count))
        } else if (_uiState.value is GiftListUiState.Success) {
            val currentItems =
                (_uiState.value as GiftListUiState.Success<GiftRankListBean.GiftRankItem>).items
            _uiState.value =
                (_uiState.value as GiftListUiState.Success<GiftRankListBean.GiftRankItem>).copy(
                    items = currentItems + data,
                    loadState = if (data.size < TopicRepository.PAGE_SIZE) LoadState.LOAD_MORE_END else LoadState.LOAD_MORE_COMPLETE,
                    page = currentPage
                )
        }
        currentPage++ // 下次加载下一页
    }

    /**
     * 处理加载错误
     */
    private fun handleError(exception: Throwable, isRefresh: Boolean) {
        if (isRefresh) {
            _uiState.value = GiftListUiState.Error(exception.message ?: "unknown error")
        } else if (_uiState.value is GiftListUiState.Success) {
            _uiState.value =
                (_uiState.value as GiftListUiState.Success<GiftRankListBean.GiftRankItem>).copy(
                    loadState = LoadState.LOAD_MORE_FAIL
                )
        }
    }
}


sealed class GiftListUiState<out T> {
    // 初始加载状态
    data object Loading : GiftListUiState<Nothing>()

    // 空数据状态 - 只在第一页没获取到数据时显示
    data object Empty : GiftListUiState<Nothing>()

    // 数据状态 - 包含所有与数据相关的状态
    data class Success<T>(
        val loadState: LoadState = LoadState.LOAD_MORE_COMPLETE,
        val page: Int = 1,
        val items: List<T> = emptyList(),
    ) : GiftListUiState<T>()

    // 错误状态
    data class Error(val message: String) : GiftListUiState<Nothing>()
}

sealed class GiftListUiEvent {
    data class UpdatePeopleCount(val count: Int) : GiftListUiEvent()
}