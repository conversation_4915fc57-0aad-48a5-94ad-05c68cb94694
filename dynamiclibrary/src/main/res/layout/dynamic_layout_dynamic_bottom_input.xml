<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/gray_e5"
        app:layout_constraintBottom_toTopOf="@+id/etInput"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/etInput"
        android:layout_width="0dp"
        android:layout_height="32dp"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="18dp"
        android:layout_marginTop="7dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="7dp"
        android:autoLink="all"
        android:background="@drawable/bg_solid_222_radius_100"
        android:backgroundTint="@color/gray_f2"
        android:gravity="start|center_vertical"
        android:layoutDirection="ltr"
        android:paddingStart="12dp"
        android:paddingLeft="12dp"
        android:paddingEnd="12dp"
        android:paddingRight="12dp"
        android:singleLine="false"
        android:textColor="@color/gray_3"
        android:textColorHint="@color/gray_c"
        android:textCursorDrawable="@null"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvSend"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vLine" />

    <CheckBox
        android:id="@+id/btnEmoticon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="6dp"
        android:background="@drawable/icon_sendmsg_biaoqing"
        android:button="@null"
        android:checked="false"
        android:layoutDirection="ltr"
        android:scaleType="center"
        app:layout_constraintBottom_toBottomOf="@+id/etInput"
        app:layout_constraintEnd_toEndOf="@+id/etInput"
        app:layout_constraintTop_toTopOf="@id/etInput" />

    <TextView
        android:id="@+id/tvSend"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/selector_btn_blue_grey_main_color_r100"
        android:enabled="false"
        android:gravity="center"
        android:layoutDirection="ltr"
        android:paddingStart="14dp"
        android:paddingEnd="14dp"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/etInput"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/etInput"
        tools:text="@string/send" />

    <View
        android:id="@+id/vLineBottom"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/gray_e5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>