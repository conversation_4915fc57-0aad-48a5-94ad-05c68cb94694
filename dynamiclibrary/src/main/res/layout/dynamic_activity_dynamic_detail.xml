<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinatorLayout"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:background="@color/white"
    app:layout_constraintBottom_toTopOf="@+id/input"
    app:layout_constraintTop_toTopOf="parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                app:layout_scrollFlags="scroll">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/llTitle"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivBack"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="6dp"
                        android:src="@drawable/icon_back_black"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#343434"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:itemCount="1"
                    tools:listitem="@layout/dynamic_item_dynamic" />

                <include
                    android:id="@+id/topicLayout"
                    android:layout_marginTop="12dp"
                    layout="@layout/dynamic_layout_dynamic_detail_topic"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp" />

                <View
                    android:id="@+id/viewLineBottom"
                    android:layout_width="match_parent"
                    android:layout_height="6dp"
                    android:layout_marginTop="16dp"
                    android:background="#f7f7f7"
                    android:visibility="gone" />

            </LinearLayout>

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:visibility="gone"
                app:layout_collapseMode="pin">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="45dp">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivBack2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/icon_back_black"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.constraintlayout.utils.widget.ImageFilterView
                        android:id="@+id/ivAvatar"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginStart="4dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/ivBack2"
                        app:layout_constraintTop_toTopOf="parent"
                        app:round="14dp" />

                    <TextView
                        android:id="@+id/tvNickName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:maxLines="1"
                        android:textColor="#343434"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/ivFav"
                        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivFav"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:src="@drawable/dynamic_ic_dynamic_fav"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@+id/tvMore"
                        app:layout_constraintEnd_toStartOf="@+id/tvMore"
                        app:layout_constraintTop_toTopOf="@+id/tvMore"
                        tools:visibility="visible" />

                    <ImageView
                        android:id="@+id/tvMore"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:contentDescription="@null"
                        android:foreground="@drawable/selector_click_circle"
                        android:scaleType="centerInside"
                        android:src="@drawable/dynamic_ic_dynamic_menu"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </com.google.android.material.appbar.MaterialToolbar>

        </com.google.android.material.appbar.CollapsingToolbarLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clBottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white">

            <TextView
                android:id="@+id/tvCommentCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="12dp"
                android:textColor="#333333"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="SpUsage"
                tools:text="评论回复(86）"
                tools:visibility="visible" />

            <androidx.cardview.widget.CardView
                android:id="@+id/viewCommentLine"
                android:layout_width="8dp"
                android:layout_height="2dp"
                android:layout_marginTop="1dp"
                app:cardBackgroundColor="#333333"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="@+id/tvCommentCount"
                app:layout_constraintStart_toStartOf="@+id/tvCommentCount"
                app:layout_constraintTop_toBottomOf="@+id/tvCommentCount" />

            <TextView
                android:id="@+id/tvGift"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:textColor="#999999"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvCommentCount"
                tools:ignore="SpUsage"
                tools:text="打赏"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvVote"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:textColor="#999999"
                android:textSize="14dp"
                app:layout_constraintEnd_toStartOf="@+id/tvGift"
                app:layout_constraintTop_toTopOf="@+id/tvCommentCount"
                tools:ignore="SpUsage"
                tools:text="投票"
                tools:visibility="visible" />

            <androidx.cardview.widget.CardView
                android:id="@+id/viewGiftLine"
                android:layout_width="8dp"
                android:layout_height="2dp"
                android:layout_marginTop="1dp"
                android:visibility="gone"
                app:cardBackgroundColor="#333333"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="@+id/tvGift"
                app:layout_constraintStart_toStartOf="@+id/tvGift"
                app:layout_constraintTop_toBottomOf="@+id/tvGift"
                tools:visibility="visible" />

            <androidx.cardview.widget.CardView
                android:id="@+id/viewVoteLine"
                android:layout_width="8dp"
                android:layout_height="2dp"
                android:layout_marginTop="1dp"
                android:visibility="gone"
                app:cardBackgroundColor="#333333"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="@+id/tvVote"
                app:layout_constraintStart_toStartOf="@+id/tvVote"
                app:layout_constraintTop_toBottomOf="@+id/tvVote"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="50dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    <include
        android:id="@+id/input"
        layout="@layout/dynamic_layout_dynamic_detail_bottom_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>