<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- versionName 第二位，单数为普通版，双数为美颜版，同一天版本更新，从1开始依次递增 -->
    <!-- 权限声明 -->
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" /> <!-- 访问网络状态 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 控制呼吸灯，振动器等，用于新消息提醒 -->
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" /> <!-- android 9.0上使用前台服务，需要添加权限 -->
    <uses-permission android:name="com.android.vending.BILLING" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- 悬浮窗 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 位置信息 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!-- 外置存储存取权限 -->
    <!-- Devices running Android 12L (API level 32) or lower  -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <!-- To handle the reselection within the app on devices running Android 14
         or higher if your app targets Android 14 (API level 34) or higher.  -->
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />


    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" /> <!-- 定位 -->
    <!-- 和下面的 uses-permission 一起加入到你的 AndroidManifest 文件中。 -->
    <permission
        android:name="${applicationId}.permission.RECEIVE_MSG"
        android:protectionLevel="signature" /> <!-- 接收 SDK 消息广播权限， 第三方 APP 接入时，请将 包名 替换为自己的包名 -->
    <uses-permission android:name="${applicationId}.permission.RECEIVE_MSG" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission
        android:name="android.permission.GET_ACCOUNTS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_SMS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_CONTACTS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.WRITE_CONTACTS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.GET_TASKS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.ACCESS_BACKGROUND_LOCATION"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" /> <!-- 获取手机号码 -->
    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" /> <!-- 华为红点 -->
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE " />

    <application
        android:name=".MyApplication"
        android:allowBackup="false"
        android:allowNativeHeapPointerTagging="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/application_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:preserveLegacyExternalStorage="true"
        android:requestLegacyExternalStorage="true"
        android:screenOrientation="portrait"
        android:supportsRtl="true"
        android:theme="@style/AppTheme.NoActionBar"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup,android:label"
        tools:overrideLibrary="com.baitu.gift">

        <uses-native-library
            android:name="libOpenCL.so"
            android:required="false" />
        <uses-native-library
            android:name="libGLES_mali.so"
            android:required="false" />
        <uses-native-library
            android:name="libmali.so"
            android:required="false" />
        <uses-native-library
            android:name="libPVROCL.so"
            android:required="false" />
        <uses-native-library
            android:name="libpocl.so"
            android:required="false" />
        <uses-native-library
            android:name="libOpenCL-pixel.so"
            android:required="false" />


        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <!--搭载 Android 4.4（API 级别 19）到 Android 10（API 级别 29）的旧款设备，以及搭载 Android 11 或 12 且支持
        Google Play 服务的 Android Go 设备，都可以安装向后移植的照片选择器版本。如需通过 Google Play
        服务自动安装向后移植的照片选择器模块，请将以下条目添加到应用清单文件的
        <application>标记中：-->

        <!-- Trigger Google Play services to install the backported photo picker module. -->
        <service
            android:name="com.google.android.gms.metadata.ModuleDependencies"
            android:enabled="false"
            android:exported="false"
            tools:ignore="MissingClass">
            <intent-filter>
                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
            </intent-filter>
            <meta-data
                android:name="photopicker_activity:0:required"
                android:value="" />
        </service>

        <activity
            android:name=".modules.me.LanguageChangeActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <meta-data
            android:name="EasyGoClient"
            android:value="true" />

        <!-- 接受处理TikTok回调数据的透明Activity -->
        <activity
            android:name=".modules.login.TikTokAuthCallbackActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.Transparent">

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="poppolive.onelink.me"
                    android:pathPrefix="/s4O6"
                    android:scheme="https" />
            </intent-filter>
        </activity>

        <activity
            android:name=".modules.SplashActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar.SplashActivity2"
            tools:ignore="AppLinkUrlError">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="dgkq6648" />
                <data android:scheme="poppo" />
            </intent-filter>
        </activity> <!-- 启动页广告 -->
        <activity
            android:name=".modules.AdSplashActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar.SplashActivity2" />

        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="com.facebook.app.FacebookContentProvider716233099083287"
            android:exported="true" /> <!-- 邀请好友专用的启动页 -->
        <activity
            android:name=".modules.InviteSplashActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar.SplashActivity2">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="poppolive.onelink.me"
                    android:pathPrefix="/s4O6"
                    android:scheme="https" />
            </intent-filter>
        </activity> <!-- 推送专用的启动页 -->
        <activity
            android:name=".modules.PushSplashActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar.SplashActivity2">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${applicationId}"
                    android:pathPrefix="/conversation/"
                    android:scheme="rong" />
            </intent-filter>
        </activity> <!-- 推送专用的启动页 拦截开发者后台不落地通知的点击事件 -->
        <activity
            android:name=".modules.PushRongSplashActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar.SplashActivity2">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${applicationId}"
                    android:pathPrefix="/push_message"
                    android:scheme="rong" />
            </intent-filter>
        </activity> <!-- 多条消息推送专用的启动页 -->
        <activity
            android:name=".modules.PushConversationSplashActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar.SplashActivity2">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${applicationId}"
                    android:pathPrefix="/conversationlist"
                    android:scheme="rong" />
            </intent-filter>
        </activity>

        <activity
            android:name=".modules.main.MainActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar.MainActivity"
            android:windowSoftInputMode="adjustNothing|stateAlwaysHidden" />
        <activity
            android:name=".modules.homepage.HomepageActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="standard"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.homepage.GiftListActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.me.EditInformationNicknameActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.me.EditInformationEditorActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.me.EditInformationProvinceActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.me.EditInformationCityActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.me.SettingActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.me.blacklist.BlackListActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".modules.search.SearchActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.search.SearchMessageActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.LockPushActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait"
            android:taskAffinity=""
            android:theme="@android:style/Theme.Wallpaper.NoTitleBar" />
        <activity
            android:name=".modules.me.RechargeActivity"
            android:configChanges="screenSize|orientation|keyboard|navigation|layoutDirection|smallestScreenSize"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.themeevent.comment.ThemeEventCommentDialog"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/DialogActivityTheme" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider> <!-- 百度定位 -->
        <!-- <meta-data -->
        <!-- android:name="com.baidu.lbsapi.API_KEY" -->
        <!-- android:value="${baidu_lbsapi_API_KEY}" /> -->
        <!-- <service -->
        <!-- android:name="com.baidu.location.f" -->
        <!-- android:enabled="true" -->
        <!-- android:process=":remote" /> -->
        <!-- 通知 -->
        <activity
            android:name=".modules.news.NoticeActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.me.auth.AuthNameActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.me.VideoPlayerDefaultActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.me.PrivacySettingsActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.login.LoginActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.login.PhoneLoginActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.login.AccountLoginActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.me.teenager.TeenagerPwdActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.me.teenager.TeenagerActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.androidrtc.im.ui.chatlist.SystemMessagesActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.androidrtc.im.ui.chatlist.SystemMessagesSubActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.dynamic.DynamicDetailActivity2"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 图片预览页面 -->
        <activity
            android:name="com.draggable.library.extension.ImagesViewerActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/DraggableTranslucentTheme" />
        <activity
            android:name=".modules.chat.ChatUpWordsActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" /> <!-- 认证页面 -->
        <activity
            android:name=".modules.me.auth.AuthActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.me.vip.setting.VipPrivilegeSettingActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/DialogActivityTheme" />
        <activity
            android:name=".modules.me.vip.show.VipPrivilegeShowActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/DialogActivityTheme" />
        <activity
            android:name=".modules.me.vip.guard.chooseguard.ChooseGuardActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/DialogActivityTheme" />
        <activity
            android:name=".modules.me.vip.VipActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/DialogActivityTheme" />
        <activity
            android:name=".modules.me.vip.GuardActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/DialogActivityTheme" />
        <activity
            android:name=".modules.me.vip.guard.fragment.GuardListActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/DialogActivityTheme" />

        <meta-data
            android:name="com.facebook.sdk.AutoInitEnabled"
            android:value="true" />
        <meta-data
            android:name="com.facebook.sdk.AutoLogAppEventsEnabled"
            android:value="false" />
        <meta-data
            android:name="com.facebook.sdk.AdvertiserIDCollectionEnabled"
            android:value="false" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />
        <meta-data
            android:name="com.alibaba.pdns.ACCESS_KEY_ID"
            android:value="LTAI5tNoH8nW9MvUywvTD9Zq" />
        <meta-data
            android:name="com.alibaba.pdns.ACCESS_KEY_SECRET"
            android:value="******************************" />

        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:exported="false"
            android:label="@string/app_name" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:label="@string/app_name">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>
        <activity
            android:name=".modules.me.editprofile.EditProfileActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 手机号注册 -->
        <activity
            android:name=".modules.login.VerifyRegisterByEmailActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" /> <!-- 邮箱密码 -->
        <activity
            android:name=".modules.login.VerifyRegisterByPhoneActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" /> <!-- 手机号密码 -->
        <activity
            android:name=".modules.login.InputPasswordActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" /> <!-- 完善信息 -->
        <activity
            android:name=".modules.login.PerfectInfoActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" /> <!-- 选择性别 -->
        <activity
            android:name=".modules.login.SelectGenderActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" /> <!-- 打招呼问候语 -->
        <activity
            android:name=".modules.homepage.SetSayHelloActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 小视频评论输入弹窗 -->
        <activity
            android:name=".modules.dynamic.DynamicVideoCommentDialog"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/DialogActivityTheme" /> <!-- 小视频详情 -->
        <activity
            android:name=".modules.dynamic.SpontaneousMomentsDetailActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 编辑自我介绍 -->
        <activity
            android:name=".modules.me.EditInformationIntroductionActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.me.auth.RealPersonActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 活体检测页面 -->
        <activity
            android:name=".modules.me.auth.RealFaceActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.safety.AccountSecurityActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.safety.email.SafetyEmailActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.safety.phone.SafetyPhoneActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.safety.verify.SafetyVerifyActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.safety.bind.SelectBindActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.me.LogoutAccountActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.me.MessageSettingActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.login.LocaleSelectorActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.login.LocaleSelectorActivity2"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.login.ResetChooseVerifyWayActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.login.ResetPasswordInputEmailAddressActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.login.ReVerifyChooseWayActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".modules.login.ReverifyInputEmailAddressActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".modules.login.ReverifyInputPhoneNumberActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />


        <activity
            android:name=".modules.login.ReverifyInputEmailVerifyCodeActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.login.ReverifyInputPhoneVerifyCodeActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.login.EmailLoginActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.login.ResetPasswordInputPhoneNumberActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- [START firebase_service] -->
        <activity
            android:name=".modules.contacts.ContactsActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.me.ChangePasswordActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".modules.themeevent.ThemeEventActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".modules.themeevent.ThemeEventDetailActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".modules.themeevent.ThemeOpenEventActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.themeevent.add.AddThemeEventActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".modules.topic.MoreTopicActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".modules.topic.HomepageUserMoreTopicActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".modules.dynamic.anonymous.add.AddAnonymousDynamicActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.dynamic.anonymous.homepage.AnonymousUserHomePageActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.dynamic.anonymous.detail.AnonymousDynamicDetailActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.dynamic.anonymous.comment.AnonymousDynamicCommentDialog"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/DialogActivityTheme" />
        <activity
            android:name=".modules.me.interest.InterestTagActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".modules.news.recommend.setting.RecommendFriendSettingActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".modules.qrcode.QRCodeAuthActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <meta-data
            android:name="firebase_messaging_auto_init_enabled"
            android:value="false" />
        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="false" />

        <service
            android:name="io.rong.push.platform.google.RongFirebaseMessagingService"
            android:exported="true"
            android:stopWithTask="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service> <!-- [END firebase_service] -->
        <!-- imlib config begin -->
        <service
            android:name="io.rong.imlib.ipc.RongService"
            android:exported="false"
            android:process=":ipc" />

        <receiver
            android:name="io.rong.imlib.ConnectChangeReceiver"
            android:exported="false" />
        <receiver
            android:name="io.rong.imlib.HeartbeatReceiver"
            android:exported="false"
            android:process=":ipc" /> <!-- imlib config end -->
        <receiver
            android:name="io.rong.push.rongpush.PushReceiver"
            android:exported="true" />
        <receiver
            android:name="com.appsflyer.SingleInstallBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.huawei.hms.support.api.push.PushMsgReceiver"
            android:directBootAware="true"
            android:exported="true"
            android:permission="${applicationId}.permission.PROCESS_PUSH_MSG">
            <intent-filter>

                <!-- Mandatory, be used to receive notification bar message click event. -->
                <action android:name="com.huawei.intent.action.PUSH_DELAY_NOTIFY" />
                <!-- Optional, compatible with old huawei phones. -->
                <action android:name="com.huawei.intent.action.PUSH" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.huawei.hms.support.api.push.PushReceiver"
            android:directBootAware="true"
            android:exported="true"
            android:permission="${applicationId}.permission.PROCESS_PUSH_MSG">
            <intent-filter>

                <!-- Mandatory, be used to receive tokens. -->
                <action android:name="com.huawei.android.push.intent.REGISTRATION" />
                <!-- Mandatory, be used to receive messages. -->
                <action android:name="com.huawei.android.push.intent.RECEIVE" />
            </intent-filter>
        </receiver>
    </application>

</manifest>