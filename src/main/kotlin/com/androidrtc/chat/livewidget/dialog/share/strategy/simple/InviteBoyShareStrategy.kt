package com.androidrtc.chat.livewidget.dialog.share.strategy.simple

import com.androidrtc.chat.livewidget.dialog.share.strategy.ShareStrategy
import com.androidtool.common.constants.ShareChannel
import com.androidtool.common.pop.BaseShareDialogFragment
import com.androidtool.common.utils.TranslateResource
import com.model.ShareInfo

/**
 * @author: mao.ye
 * @updateTime: 2024-09-14
 * @description: 普通邀请InviteBoy分享获取策略
 */
class InviteBoyShareStrategy(private var shareInfo: ShareInfo) : ShareStrategy {

    override fun share(channel: ShareChannel, shareDialogFragment: BaseShareDialogFragment) {
        val context = shareDialogFragment.context ?: return
        BaseShareDialogFragment.generateInviteLink(context, channel.value) { link ->
            shareDialogFragment.share(
                channel,
                "$link ${TranslateResource.getStringResources("download_app_find_friends")}",
                shareInfo.from
            )

        }
    }

}