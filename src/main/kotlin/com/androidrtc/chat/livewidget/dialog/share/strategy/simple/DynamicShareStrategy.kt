package com.androidrtc.chat.livewidget.dialog.share.strategy.simple

import com.model.ShareInfo
import com.androidrtc.chat.livewidget.dialog.share.strategy.ShareStrategy
import com.androidtool.common.constants.ShareChannel
import com.androidtool.common.pop.BaseShareDialogFragment

/**
 * @author: mao.ye
 * @updateTime: 2024-09-14
 * @description: 动态相关的分享获取策略
 */
class DynamicShareStrategy(private var shareInfo: ShareInfo) : ShareStrategy {
    override fun share(channel: ShareChannel, shareDialogFragment: BaseShareDialogFragment) {
        shareDialogFragment.share(channel, shareInfo.shareUrl, shareInfo.from)
    }
}