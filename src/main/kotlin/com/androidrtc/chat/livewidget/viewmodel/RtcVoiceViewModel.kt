package com.androidrtc.chat.livewidget.viewmodel

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.androidtool.CommonApp
import com.androidtool.common.base.BaseViewModel
import com.androidtool.common.constants.FileDirs
import com.androidtool.common.net.Apis
import com.androidtool.common.utils.AsyncTaskUtil
import com.androidtool.common.utils.DownloadManager
import com.androidtool.common.utils.OtherUtils
import com.androidtool.common.utils.performRequest
import com.baitu.rtc.IAVChatService
import com.model.RtcVoiceItem
import java.io.File

/**
 * 氛围声相关接口逻辑
 */
class RtcVoiceViewModel : BaseViewModel() {

    private val voiceListData = MutableLiveData<List<RtcVoiceItem>>()
    val voiceListLiveData: LiveData<List<RtcVoiceItem>> = voiceListData
    fun getVoiceList(
        owner: LifecycleOwner? = null
    ) {
        apiService.getRtcVoiceList()
            .performRequest({
                it.data?.list?.let { data ->
                    voiceListData.value = data
                }
            }, {

            }, owner, Lifecycle.Event.ON_DESTROY)
    }

    /**
     * 预加载所有音频文件到本地
     */
    fun preloadVoiceList(owner: LifecycleOwner? = null) {
        getVoiceList()
        owner?.let {
            voiceListLiveData.observe(it) { listData ->
                listData?.forEach { dataItem ->
                    downloadRtcVoice(dataItem.audio) { audio ->
                        if (audio.isNotEmpty() && dataItem.id != null)
                            (CommonApp.liveBridge?.getLiveRoomService() as? IAVChatService?)?.preloadRTCVoice(
                                dataItem.id!!, audio
                            )

                    }
                }
            }
        }
    }

    /**
     * 下载氛围声文件
     */
    fun downloadRtcVoice(path: String?, callback: (String) -> Unit) {
        if (path == null) callback.invoke("")
        val voiceFile = File(FileDirs.rtcVoiceFile, OtherUtils.getFileName(path!!))
        if (voiceFile.exists()) {
            // 本地存在
            callback.invoke(voiceFile.absolutePath)
            return
        }
        DownloadManager.download(
            Apis.getAssetsFullUrl(path),
            FileDirs.rtcVoiceFile
        ) { status, _, _, file ->
            if (status == DownloadManager.DownloadStatus.SUCCESS) {
                if (file != null) {
                    // 文件下载成功
                    callback.invoke(file.absolutePath)
                }
            } else if (status == DownloadManager.DownloadStatus.FAILED || status == DownloadManager.DownloadStatus.CANCELLED) {
                // 失败
                AsyncTaskUtil.executeMainIntensiveTask(this) {
                    callback.invoke("")
                }
            }

        }

    }
}