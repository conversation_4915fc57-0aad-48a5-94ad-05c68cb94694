package com.androidrtc.chat.livewidget.viewmodel

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.androidrtc.chat.livewidget.LiveWidgetApp
import com.androidtool.CommonApp
import com.androidtool.common.ToastUtils
import com.androidtool.common.base.BaseViewModel
import com.androidtool.common.constants.CreateInType
import com.androidtool.common.troll.BaseResponse
import com.androidtool.common.utils.AsyncTaskUtil
import com.androidtool.common.utils.JSONUtil
import com.androidtool.common.utils.TranslateResource
import com.androidtool.common.utils.performRequest
import com.androidtool.common.utils.toIntFix
import com.model.UserInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 *
 * @ProjectName:    globalAndroid
 * @Package:        com.androidrtc.chat.refactor.module.live.viewModel
 * @ClassName:      LiveEndViewModel
 * @Description:     直播结束viewModel
 * @Author:         wangll
 * @CreateDate:     2021/7/9 10:08 上午
 * @UpdateUser:     更新者
 * @UpdateDate:     2021/7/9 10:08 上午
 * @UpdateRemark:   更新说明
 * @Version:        1.0
 */
class UserCardViewModel : BaseViewModel() {

    var userInfo: UserInfo? = null


    /**
     * @method
     * @description 从user/info里面获取一些参数配置
     * infos 传入你想获取的参数
     * @date: 2021/7/9 11:28 上午
     * @author: wangll
     * @param  * @param null
     * @return
     */
    fun getUserInfoConfig(
        infos: String,
        uid: String,
        onNext: (UserInfo) -> Unit,
        owner: LifecycleOwner? = null,
    ) {
        if (uid.isEmpty()) return
        val map = mutableMapOf(
            Pair("infos", infos),
            Pair("uid", uid)
        )
        apiService
            .getUserInfo(
                map
            )
            .performRequest({
                it.data?.run {
                    onNext.invoke(this)
                }
            }, {}, owner, Lifecycle.Event.ON_DESTROY)
    }

    /**
     * @method
     * @description 获取用户信息
     * @date: 2021/7/9 11:28 上午
     * @author: wangll
     * @param  * @param null
     * @return
     */

    fun getUserInfo(
        uid: String,
        groupId: String,
        onNext: (UserInfo) -> Unit,
        owner: LifecycleOwner? = null,
    ) {
        if (uid.isEmpty()) return
        uid.run {
            var infos =
                "report_action|niceid_level|homepage_card|contributor|medals_count|honor_tags|global_country_icon|global_country_icon_full|video_chat_jia_text|nice_number|medals|live_notice|avatar_frame|theme|group|coin_seller|coin_seller_level|is_in_live|in_room|in_room_server|avatar|state|vip_data|nickname|remark_name" +
                        "|gender|age|wealth_level|recent_wealth_level|live_level" +
                        "|live_grade_score|chat_grade_score" +
                        "|province|city|distance_text|sign" +
                        "|club_is_join_history|quality_agent" +
                        "|can_kick|is_admin|room_in_talk|is_police" +
                        "|is_fav|can_shut_up|shut_up_state|voice_sign|has_club|club_level|club_name|club_pick|user_show_check_in" +
                        "|can_mute_seat|can_close_seat|uid|country_number|can_invite_talk|game_max_fruit|game_max_fastthree|game_max_goldenflower|game_max_slotgame|live_progress|wealth_progress|ward_star|fans" +
                        "|gift_wall_summary|gift_hall_info|is_hide_live_level"
            // 如果是主咖模式需要查询是否可以设置主咖
            if (LiveWidgetApp.liveWidgetBridge?.isBigPartyRoom() == true
                && (LiveWidgetApp.liveWidgetBridge?.getSeatByUid(uid) ?: 0) > 0
            ) {
                infos = "$infos|can_set_main_guest" // 主咖
            }
            val map = mutableMapOf(
                Pair("infos", infos),
                Pair("created_in", CreateInType.ROOM.value),
                Pair("created_in_id", LiveWidgetApp.liveWidgetBridge?.getRoomId() ?: ""),
                Pair("type", ""),
                Pair("uid", uid),
                Pair("page", ""),
                Pair("fan_club_id", groupId),
                Pair("club_group_id", groupId)

            )
            // 加载数据库中的内容
            AsyncTaskUtil.executeIOIntensiveTask(this@UserCardViewModel) {
                val userJson = CommonApp.dbHelper?.getUserCard(uid)
                if (userJson.isNullOrEmpty()) return@executeIOIntensiveTask
                val userInfo = JSONUtil.fromJSON(userJson, UserInfo::class.java)
                withContext(Dispatchers.Main) {
                    if (userInfo != null) {
                        onNext.invoke(userInfo)
                        <EMAIL> = userInfo
                    }
                }

            }
            apiService
                .getUserInfo(
                    map
                )
                .performRequest({
                    it.data?.run {
                        // 如果数据库中的内容和接口返回的内容相同就不要再次回调了
                        if (userInfo != null && JSONUtil.toJSON(this) == JSONUtil.toJSON(userInfo!!)) {
                            // 不需要更新数据
                        } else {
                            onNext.invoke(this)
                            userInfo = this@run
                            // 缓存到数据库
                            AsyncTaskUtil.executeIOIntensiveTask(this@UserCardViewModel) {
                                CommonApp.dbHelper?.putUserCardTable(uid, JSONUtil.toJSON(this))
                            }
                        }
                    }
                }, {}, owner, Lifecycle.Event.ON_DESTROY)
        }

    }


    /**
     * 是否是房间管理员
     */
    fun isAdmin(uid: String, onNext: (UserInfo) -> Unit, owner: LifecycleOwner? = null) {
        if (uid.isEmpty()) return
        uid.run {
            val infos = "is_admin"
            val map = mutableMapOf(
                Pair("infos", infos),
                Pair("created_in", CreateInType.ROOM.value),
                Pair("created_in_id", LiveWidgetApp.liveWidgetBridge?.getRoomId() ?: ""),
                Pair("type", ""),
                Pair("uid", uid),
                Pair("page", ""),
                Pair("fan_club_id", "")
            )
            apiService
                .getUserInfo(map)
                .performRequest({
                    it.data?.run {
                        onNext.invoke(this)
                    }
                }, {}, owner, Lifecycle.Event.ON_DESTROY)
        }
    }

    fun muteChat(
        uid: String,
        groupId: String,
        callback: () -> Unit,
        owner: LifecycleOwner? = null,
    ) {
        if (uid.isEmpty() || groupId.isEmpty()) { // 参数校验
            return
        }
        val params = mutableMapOf(
            Pair("club_group_id", groupId),
            Pair("id", groupId),
            Pair("to_uid", uid)
        )
        apiService.groupChatMute(params).performRequest({
            // 发送禁言消息
            it.data?.let { it1 ->
                CommonApp.imBridge
                    ?.sentRongMessage(it1, true)
            }
            callback.invoke()
        }, {}, owner, Lifecycle.Event.ON_DESTROY)
    }


    /**
     * 添加关注
     */
    fun addFans(
        fuid: String,
        onNext: (Any) -> Unit,
        onCallBackError: (Throwable) -> Unit = {}, owner: LifecycleOwner? = null,
    ) {
        if (fuid.isEmpty()) return
        val params =
            mutableMapOf(
                Pair("fuid", fuid),
                Pair("roomid", LiveWidgetApp.liveWidgetBridge?.getRoomId() ?: ""),
                Pair("created_in", CreateInType.ROOM.value),
                Pair("created_in_id", LiveWidgetApp.liveWidgetBridge?.getRoomId() ?: ""),
            )
        apiService
            .addFav(
                params
            ).performRequest({
                onNext.invoke(it)
            }, {
                onCallBackError.invoke(it)
            }, owner, Lifecycle.Event.ON_DESTROY)

    }


    /**
     * 去禁言
     */
    fun doMute(
        uid: String,
        room_id: String,
        onNext: (BaseResponse<String>) -> Unit,
        onCallBackError: (Throwable) -> Unit = {},
        owner: LifecycleOwner? = null,
    ) {
        if (uid.isEmpty()) return
        val params = mutableMapOf(Pair("uid", uid), Pair("id", room_id))
        apiService
            .roomShutUp(params)
            .performRequest({
                onNext.invoke(it)
            }, {
                onCallBackError.invoke(it)
            }, owner, Lifecycle.Event.ON_DESTROY)

    }


    /**
     * 去踢出
     */
    fun doKickOut(
        uid: String,
        room_id: String,
        onNext: (BaseResponse<String>) -> Unit,
        onCallBackError: (Throwable) -> Unit = {},
        owner: LifecycleOwner? = null,
    ) {
        if (uid.isEmpty()) return
        val params = mutableMapOf(Pair("uid", uid), Pair("id", room_id))
        apiService
            .kick(params)
            .performRequest({
                onNext.invoke(it)
            }, {
                onCallBackError.invoke(it)
            }, owner, Lifecycle.Event.ON_DESTROY)

    }


    /**
     * 房间设置管理员 uid被操作的人 id 房间号
     */
    fun setAdmin(
        uid: String,
        room_id: String,
        onNext: (BaseResponse<String>) -> Unit,
        onCallBackError: (Throwable) -> Unit = {},
        owner: LifecycleOwner? = null,
    ) {
        if (uid.isEmpty()) return
        val params =
            mutableMapOf(
                Pair("uid", uid),
                Pair("id", room_id)
            )
        apiService
            .setAdmin(params)
            .performRequest({
                onNext.invoke(it)
            }, {
                onCallBackError.invoke(it)
            }, owner, Lifecycle.Event.ON_DESTROY)

    }

    fun endTalk(uid: String, owner: LifecycleOwner? = null, callback: () -> Unit) {
        if (uid.isEmpty()) return
        val params = mutableMapOf(
            Pair("id", LiveWidgetApp.liveWidgetBridge?.getRoomId() ?: ""),
            Pair("uid", uid),
            Pair("created_from", "agree")
        )
        apiService.endTalk(params).performRequest({
            it.data?.run {
                // 被下麦
                if (LiveWidgetApp.liveWidgetBridge?.getCurrentPlayingVideoUid() == uid) { // 停止视频拉流
                    LiveWidgetApp.liveWidgetBridge?.getLiveRoomService()
                        ?.stopRemotePreview(uid.toIntFix())
                }
                LiveWidgetApp.liveWidgetBridge?.getTalkUserListLiveData()?.postValue(this)
            }
            callback.invoke()
        }, {
        }, owner, Lifecycle.Event.ON_DESTROY)
    }


    fun inviteTalk(uid: String, owner: LifecycleOwner? = null, callback: () -> Unit) {
        if (uid.isEmpty()) return
        val params = mutableMapOf(
            Pair("id", LiveWidgetApp.liveWidgetBridge?.getRoomId() ?: ""),
            Pair("uid", uid)
        )
        apiService.inviteTalk(params).performRequest({
            callback.invoke()
        }, {
        }, owner, Lifecycle.Event.ON_DESTROY)
    }


    fun openMic(uid: String, seat: Int, owner: LifecycleOwner? = null, callback: () -> Unit) {
        if (uid.isEmpty()) return
        val params = mutableMapOf(
            Pair("seat", seat.toString()),
            Pair("uid", uid),
            Pair("id", LiveWidgetApp.liveWidgetBridge?.getRoomId() ?: "")
        )
        apiService.openMic(params).performRequest({
            callback.invoke()
        }, {
        }, owner, Lifecycle.Event.ON_DESTROY)
    }

    fun closeMic(uid: String, seat: Int, owner: LifecycleOwner? = null, callback: () -> Unit) {
        if (uid.isEmpty()) return
        val params = mutableMapOf(
            Pair("seat", seat.toString()),
            Pair("uid", uid),
            Pair("id", LiveWidgetApp.liveWidgetBridge?.getRoomId() ?: "")
        )
        apiService.closeMic(params).performRequest({
            callback.invoke()
        }, {
        }, owner, Lifecycle.Event.ON_DESTROY)
    }


    /**
     * @method
     * @description 修改粉丝团灯牌
     * @date: 2021/11/3 3:44 下午
     * @author: wangll
     * @param  * @param null
     * @return
     */

    fun saveGroupName(
        newGroupName: String,
        onNext: (BaseResponse<String>) -> Unit,
        onCallBackError: (Throwable) -> Unit = {},
        owner: LifecycleOwner?,
    ) {
        val params = mutableMapOf(
            Pair("name", newGroupName)
        )
        apiService
            .fansClubEditName(params)
            .performRequest({
                ToastUtils.showToast(TranslateResource.getStringResources("succed"))
                onNext.invoke(it)
            }, {
                onCallBackError.invoke(it)
            }, owner)
    }

    /**
     * party 邀请主咖
     */
    fun mainGuestInvite(uid: String) {
        if (uid.isEmpty()) return
        val params = mutableMapOf(
            Pair("uid", uid),
            Pair("id", LiveWidgetApp.liveWidgetBridge?.getRoomId() ?: "")
        )
        apiService.mainGuestInvite(params).performRequest({
            ToastUtils.showToast(TranslateResource.getStringResources("main_guest_sent"))
        }, { })
    }

    /**
     * party 取消主咖
     */
    fun mainGuestCancel(uid: String) {
        if (uid.isEmpty()) return
        val params = mutableMapOf(
            Pair("uid", uid),
            Pair("id", LiveWidgetApp.liveWidgetBridge?.getRoomId() ?: "")
        )
        apiService.mainGuestCancel(params).performRequest({

        }, {})
    }
}