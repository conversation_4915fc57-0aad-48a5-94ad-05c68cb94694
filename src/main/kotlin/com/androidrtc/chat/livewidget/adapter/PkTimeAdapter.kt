package com.androidrtc.chat.livewidget.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.widget.TextView
import com.androidrtc.chat.livewidget.R
import com.androidtool.common.utils.FontsUtil
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class PkTimeAdapter : BaseQuickAdapter<String, BaseViewHolder>(R.layout.item_pk_time) {
    var selectPosition: Int = -1

    @SuppressLint("SetTextI18n")
    override fun convert(holder: BaseViewHolder, item: String) {
        val tvItem = holder.getView<TextView>(R.id.tv_item)
        if (selectPosition == holder.layoutPosition) {
            tvItem.setTextColor(Color.WHITE)
            holder.setBackgroundResource(R.id.cl_item, R.drawable.bg_pk_time_select)
        } else {
            tvItem.setTextColor(Color.parseColor("#5259F7"))
            holder.setBackgroundResource(R.id.cl_item, R.drawable.bg_pk_time_normal)
        }
        tvItem.typeface = FontsUtil.getDDINExpBold()
        tvItem.text = "$item min"
    }
}