package com.androidrtc.chat.livewidget.view.liveinfo

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.core.content.ContextCompat
import com.androidrtc.chat.livewidget.R
import com.androidrtc.chat.livewidget.databinding.LayoutLiveLevelBinding
import com.androidrtc.chat.livewidget.model.Status
import com.androidrtc.chat.livewidget.view.widget.BaseLiveView
import com.androidtool.CommonApp
import com.androidtool.common.activity.webview.H5
import com.androidtool.common.data.PreferenceManager
import com.androidtool.common.extension.onClick
import com.androidtool.common.msgserver.MqttMsgType
import com.androidtool.common.utils.ImageRes
import com.baitu.rtc.toIntFix
import com.model.ReceiveData
import com.androidrtc.chat.livewidget.view.livemsg.model.CommonChatEntity
import com.androidrtc.chat.livewidget.view.livemsg.model.GiftChatEntity

/**
 * 直播等级控件
 * <AUTHOR>
 */
class LiveLevelView @JvmOverloads constructor(
    cxt: Context,
    attributeSet: AttributeSet? = null,
    defStyle: Int = 0
) : BaseLiveView(cxt, attributeSet, defStyle) {
    private val binding = LayoutLiveLevelBinding.inflate(LayoutInflater.from(cxt), this)


    override fun onLiveStateChange(status: Status) {
        visibility = if (isLiveIng(status) && CommonApp.liveBridge?.isGamePartyRoom() != true) {
            if (CommonApp.liveBridge?.inAnchor(PreferenceManager.Companion.instance.userId.toString()) == true) {
                // 主播端写死展示
                registerMessageType()
                initView()
                VISIBLE
            } else {
                // 观众根据字段判断是否显示
                if (CommonApp.liveBridge?.getRoomInfo()?.showIncome == 1) {
                    registerMessageType()
                    initView()
                    VISIBLE
                } else {
                    unRegisterMessageType()
                    GONE
                }
            }
        } else {
            GONE
        }
    }


    private fun initView() {
        // 等级图标
        CommonApp.liveBridge?.getRoomInfo()?.let {
            updateLevelIcon(it.liveLevel)
            updateLiveLevel(it.liveLevel, it.liveLevelPercent)
        }
        binding.root.onClick {
            if (CommonApp.configProvider?.getCName() == "mimi") {
                H5.roomContributeRank()
            }
        }
    }

    /**
     * 更新等级
     */
    private fun updateLevelIcon(level: String) {
        binding.ivLiveLevel.setImageResource(
            ImageRes.imageLiveLevelRes[level.toIntFix()
                .coerceAtMost(ImageRes.imageLiveLevelRes.size - 1)]
        )
    }


    /**
     * 更新直播等级，进度条
     */
    @SuppressLint("SetTextI18n")
    private fun updateLiveLevel(level: String, liveLevelPercent: String?) {
        if (!liveLevelPercent.isNullOrEmpty()) {
            val progress = liveLevelPercent.toDouble()
            val liveLevel = level.toIntFix()
            binding.tvProgress.text = "${progress}%"
            when {
                liveLevel < 5 -> {
                    binding.roomLiveLevelProgress.progressDrawable =
                        ContextCompat.getDrawable(context, R.drawable.bg_progress_all_radius_0_4)
                }

                liveLevel < 10 -> {
                    binding.roomLiveLevelProgress.progressDrawable =
                        ContextCompat.getDrawable(context, R.drawable.bg_progress_all_radius_5_9)
                }

                liveLevel < 15 -> {
                    binding.roomLiveLevelProgress.progressDrawable =
                        ContextCompat.getDrawable(context, R.drawable.bg_progress_all_radius_10_14)
                }

                liveLevel < 20 -> {
                    binding.roomLiveLevelProgress.progressDrawable =
                        ContextCompat.getDrawable(context, R.drawable.bg_progress_all_radius_15_19)
                }

                liveLevel < 25 -> {
                    binding.roomLiveLevelProgress.progressDrawable =
                        ContextCompat.getDrawable(context, R.drawable.bg_progress_all_radius_20_24)
                }

                liveLevel < 30 -> {
                    binding.roomLiveLevelProgress.progressDrawable =
                        ContextCompat.getDrawable(context, R.drawable.bg_progress_all_radius_25_29)
                }

                liveLevel < 35 -> {
                    binding.roomLiveLevelProgress.progressDrawable =
                        ContextCompat.getDrawable(context, R.drawable.bg_progress_all_radius_30_34)
                }

                else -> {
                    binding.roomLiveLevelProgress.progressDrawable =
                        ContextCompat.getDrawable(context, R.drawable.bg_progress_all_radius_35_39)
                }
            }
            binding.roomLiveLevelProgress.progress = progress.toInt()
        }
    }

    override fun listenerMessageType() = arrayListOf(MqttMsgType.ROOM_GIFT_MSG)

    override fun handleCustomMsg(type: String, msg: String, data: ReceiveData) {
        when (type) {
            MqttMsgType.ROOM_GIFT_MSG -> {

                // 礼物消息，更新积分,更新等级
                if (data.to?.uid == CommonApp.liveBridge?.getRoomId()) { // 自己房间的消息才更新
                    val entity = GiftChatEntity(
                        CommonChatEntity.UIType.VIDEO,
                        data.to?.room_id ?: "",
                        msg
                    )
                    // 如果本地 roomInInfo 里面的等级高于收到的消息的等级，就不更新等级信息
                    if ((CommonApp.liveBridge?.getRoomInfo()?.liveLevel?.toIntFix()
                            ?: 0) > (entity.giftLog?.toUserLiveLevel ?: 0)
                    ) return
                    updateLiveLevel(
                        entity.giftLog?.toUserLiveLevel.toString(),
                        entity.giftLog?.toUserLiveLevelPercent
                    )
                    updateLevelIcon(entity.giftLog?.toUserLiveLevel.toString())
                    val money = data.getExtraModel()?.getCoinModel()?.to?.income ?: ""
                }
            }
        }
    }


}