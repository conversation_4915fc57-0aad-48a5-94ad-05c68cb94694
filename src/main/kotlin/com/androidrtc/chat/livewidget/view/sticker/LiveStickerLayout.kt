package com.androidrtc.chat.livewidget.view.sticker

import android.content.Context
import android.os.Build
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.inputmethod.EditorInfo
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.graphics.toColorInt
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.lifecycleScope
import com.androidrtc.chat.livewidget.databinding.LayoutStickerBinding
import com.androidrtc.chat.livewidget.model.MultiPKStatus
import com.androidrtc.chat.livewidget.model.Status
import com.androidrtc.chat.livewidget.view.widget.BaseLiveView
import com.androidrtc.chat.livewidget.viewmodel.LiveViewModel
import com.androidtool.CommonApp
import com.androidtool.common.ToastUtils
import com.androidtool.common.data.PreferenceManager
import com.androidtool.common.extension.failure
import com.androidtool.common.extension.onClick
import com.androidtool.common.extension.success
import com.androidtool.common.extension.wrap
import com.androidtool.common.msgserver.MqttMsgType
import com.androidtool.common.utils.ImageLoader
import com.androidtool.common.utils.JSONUtil
import com.androidtool.common.utils.OtherUtils
import com.androidtool.common.utils.ScreenUtil
import com.androidtool.common.utils.SoftKeyboardAndWindowUtils
import com.androidtool.common.utils.TranslateResource
import com.model.ListItem
import com.model.ReceiveData
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 *
 * @ProjectName:    submodulevone$
 * @Package:        com.love.vone.live.videoroom.view$
 * @ClassName:      LiveStickerLayout$
 * @Description:    直播间贴纸视图
 * @Author:         wangll
 * @CreateDate:     2024/10/10$ 13:17$
 * @UpdateUser:     更新者
 * @UpdateDate:     2024/10/10$ 13:17$
 * @UpdateRemark:   更新内容
 * @Version:        1.0
 */
class LiveStickerLayout @JvmOverloads constructor(
    context: Context, attributeSet: AttributeSet? = null,
    defStyle: Int = 0
) : BaseLiveView(context, attributeSet, defStyle) {

    private val binding: LayoutStickerBinding =
        LayoutStickerBinding.inflate(LayoutInflater.from(context), this)


    private var viewModel: LiveViewModel? = null

    private var myListItem: ListItem? = null
    private var onLayoutChangeListener: OnLayoutChangeListener? = null

    /**
     * 观众手动关闭贴纸
     */
    private var closeSticker: Boolean = false

    /**
     * 绑定viewmodel，从创建该view的Fragment中传递过来
     */
    fun bindViewModel(viewModel: LiveViewModel) {
        this.viewModel = viewModel
    }

    override fun onLiveStateChange(status: Status) {
        if (status == Status.LIVE_END) {
            //LIVE_END时需要重置的操作
            removeSticker()
        }
    }

    override fun listenerMessageType() = arrayListOf(
        MqttMsgType.LIVE_CHARTLET,              //保存贴纸消息
    )

    override fun handleCustomMsg(type: String, msg: String, data: ReceiveData) {
        super.handleCustomMsg(type, msg, data)
        if (!closeSticker && type == MqttMsgType.LIVE_CHARTLET) {
            liveChartletMsg(msg, data)
        }
    }

    /**
     * 保存贴纸消息消息
     */
    private fun liveChartletMsg(msg: String, data: ReceiveData) {
        if (data.to?.room_id == CommonApp.liveBridge?.getRoomId()) {//本房间
            val customData = getCustomData(msg)
            customData?.run {
                val listItem =
                    JSONUtil.fromJSON(this.optString("list"), ListItem::class.java)
                if (CommonApp.liveBridge?.inAnchor(PreferenceManager.Companion.instance.userId.toString()) == false) {
                    if (listItem.chartletId.isNotEmpty()) {
                        addSticker(listItem)
                    } else {
                        removeSticker()
                    }
                }
            }
        }
    }

    /**
     * 贴纸视图
     */
    fun stickerLayout() {
        if (isMultiPK(CommonApp.liveBridge?.getMultiPKStatusLiveDataValue() as MultiPKStatus?)) {
            removeSticker()
        } else {
            chartlet()
        }
    }

    /**
     * 多人pk状态监听
     */
    override fun onMultiPKStateChange(status: MultiPKStatus) {
        super.onMultiPKStateChange(status)
        //多人PK中隐藏贴纸，PK结束显示
        if (isMultiPK(status)) {
            removeSticker()
        } else {
            //在多人pk切换直播中，才显示贴纸，预览界面和直播界面LayoutParams不一样，导致坐标点变了，贴纸位置跳动
            if (!closeSticker && CommonApp.liveBridge?.isLiveIng() == true) {
                chartlet()
            }
        }
    }


    /**
     * 贴图列表
     */
    private fun chartlet() {
        closeSticker = false
        getLifecycleOwner()?.let { lifecycleOwner ->
            viewModel?.fetchChartLet(CommonApp.liveBridge?.getRoomId() ?: "")
                ?.wrap(lifecycleOwner.lifecycleScope) {
                    success { data ->
                        if (isMultiPK(CommonApp.liveBridge?.getMultiPKStatusLiveDataValue() as MultiPKStatus?)) {
                            removeSticker()
                        } else {
                            if (data.my != null && data.my!!.chartletId.isNotEmpty()) {
                                addSticker(data.my!!)
                            } else {
                                removeSticker()
                            }
                        }
                    }
                    failure { }
                }
        }
    }


    /**
     * 添加贴纸
     */
    fun addSticker(listItem: ListItem) {
        myListItem = listItem
        //设置上下遮罩
        setStickerMask()
        //添加贴纸
        binding.stickerTextFrameLayout.visibility = VISIBLE
        binding.stickerTextFrameLayout.translationX = 0f
        binding.stickerTextFrameLayout.translationY = 0f
        //设置贴纸LayoutParams
        setStickerLayoutParams(listItem)
        ImageLoader.loadImage(this, listItem.style) {
            it?.run {
                binding.stickerTextFrameLayout.updateLayoutParams {
                    width = ScreenUtil.dp2px(it.intrinsicWidth / 3)
                    height = ScreenUtil.dp2px(it.intrinsicHeight / 3)
                }
                binding.stickerTextFrameLayout.background = this
                binding.stickerTextFrameLayout.post {
                    //设置贴纸属性和内容
                    setStickerAttribute(listItem)
                    //设置贴纸监听及回调
                    setStickerOnClick()
                    if (CommonApp.liveBridge?.inAnchor(PreferenceManager.Companion.instance.userId.toString()) == true) {
                        binding.stickerTextFrameLayout.isEnabled = true
                        binding.stickerEditTextView.isFocusable = true
                        binding.stickerEditTextView.isClickable = true
                        binding.stickerEditTextView.setOnFocusChangeListener { _, hasFocus ->
                            if (hasFocus) {
                                if (onLayoutChangeListener == null) {
                                    onLayoutChangeListener =
                                        SoftKeyboardAndWindowUtils.imeShowListener(
                                            context, (context as AppCompatActivity).window
                                        ) { height, isShow ->
                                            if (isShow && height > 0) {
                                                // 软键盘显示
                                            } else if (!isShow && height < 0) {
                                                // 软键盘隐藏
                                                OtherUtils.hideSoftInputFromWindow(binding.stickerEditTextView)
                                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                                                    (context as AppCompatActivity).window.decorView.setOnApplyWindowInsetsListener(
                                                        null
                                                    )
                                                } else {
                                                    onLayoutChangeListener?.let { listener ->
                                                        (context as AppCompatActivity).window.decorView.removeOnLayoutChangeListener(
                                                            listener
                                                        )
                                                    }
                                                }
                                                onLayoutChangeListener = null
                                            }
                                        }
                                }
                            }
                        }
                        binding.stickerEditTextView.apply {//限制字符过长并提示
                            doAfterTextChanged { p0 ->
                                try {
                                    if (!listItem.word.contentEquals(p0.toString())) {
                                        val cou =
                                            binding.stickerEditTextView.text.toString()
                                                .toByteArray(charset("GB2312")).size
                                        val mEditText = binding.stickerEditTextView
                                        val length = if (listItem.word_length.isNullOrEmpty()) {
                                            20
                                        } else {
                                            listItem.word_length!!.toInt()
                                        }
                                        if (cou > length) {
                                            val selectionStart = mEditText.selectionStart
                                            if (selectionStart <= 0) {
                                                return@doAfterTextChanged
                                            }
                                            val selectionEnd = mEditText.selectionEnd
                                            p0?.delete(selectionStart - 1, selectionEnd)
                                            ToastUtils.showToast(
                                                TranslateResource.getStringResources(
                                                    "cannt_more"
                                                )
                                            )//"输入文字过长"
                                        }
                                    }
                                } catch (e: Exception) {
                                }
                            }
                        }
                        binding.stickerEditTextView.setOnEditorActionListener { _, actionId, _ ->
                            if (actionId == EditorInfo.IME_ACTION_DONE) {
                                OtherUtils.hideSoftInputFromWindow(binding.stickerEditTextView)
                                myListItem?.run {
                                    val word =
                                        binding.stickerEditTextView.text.toString()
                                    if (word != TranslateResource.getStringResources(this.word)) {
                                        chartletSave(this)
                                    }
                                }
                                return@setOnEditorActionListener true
                            }
                            return@setOnEditorActionListener false
                        }
                        chartletSave(listItem)
                    } else {
                        binding.stickerTextFrameLayout.isEnabled = false
                        binding.stickerEditTextView.isFocusable = false
                        binding.stickerEditTextView.isClickable = true
                    }
                }
            }
        }


    }


    /**
     * 设置贴纸上下遮罩
     */
    private fun setStickerMask() {
        //设置上下遮罩
        binding.flTopMask.visibility = INVISIBLE
        binding.vBottomMask.visibility = INVISIBLE
        binding.vCropHorizontalLineTop.visibility = INVISIBLE
        binding.vCropHorizontalLineBottom.visibility = INVISIBLE
        binding.vCropVerticalLineStart.visibility = INVISIBLE
        binding.vCropVerticalLineEnd.visibility = INVISIBLE
        binding.vBottomMask.updateLayoutParams {
            height =
                ScreenUtil.getScreenHeight() / 3 + ScreenUtil.getNavBarHeight() + ScreenUtil.dp2px(1)
        }
    }

    /**
     * 设置贴纸属性和内容
     */
    private fun setStickerAttribute(listItem: ListItem) {
        if (listItem.wordColor.isNotEmpty()) {
            if (listItem.wordColor.contains("#")) {
                binding.stickerEditTextView.setTextColor(listItem.wordColor.toColorInt())
            } else {
                binding.stickerEditTextView.setTextColor("#${listItem.wordColor}".toColorInt())
            }
        }
        var size = 0f
        if (listItem.wordSize.isNotEmpty()) {
            size = listItem.wordSize.toFloat()
            binding.stickerEditTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, size)

        }
        if (listItem.wordLocationX.isNotEmpty() && listItem.wordLocationY.isNotEmpty()) {
            val paddingTop = ScreenUtil.dp2px(listItem.wordLocationY.toInt())
            binding.stickerEditTextView.updateLayoutParams<FrameLayout.LayoutParams> {
                this.width =
                    binding.stickerTextFrameLayout.width - ScreenUtil.dp2px(listItem.wordLocationX.toInt())
                this.height = binding.stickerTextFrameLayout.height - paddingTop
            }
        }
        if (listItem.wordChange == 1) {
            binding.stickerEditTextView.setText(listItem.word)
        } else {
            binding.stickerEditTextView.setText(
                TranslateResource.getStringResources(
                    listItem.word
                )
            )
        }
        binding.stickerEditTextView.setSelection(binding.stickerEditTextView.length())
    }

    /**
     * 设置贴纸监听及回调
     */
    private fun setStickerOnClick() {
        binding.stickerTextFrameLayout.deleteCallback = {
            if (binding.stickerTextFrameLayout.isVisible) {
                binding.stickerTextFrameLayout.visibility = INVISIBLE
                binding.flTopMask.visibility = INVISIBLE
                binding.vBottomMask.visibility = INVISIBLE
                binding.vCropHorizontalLineTop.visibility = INVISIBLE
                binding.vCropHorizontalLineBottom.visibility = INVISIBLE
                binding.vCropVerticalLineStart.visibility = INVISIBLE
                binding.vCropVerticalLineEnd.visibility = INVISIBLE
                if (binding.stickerTextFrameLayout.del) {
                    viewModel?.fetchChartLetDel()
                }
            }
        }
        binding.stickerTextFrameLayout.onTouchEventCallback = { event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    OtherUtils.hideSoftInputFromWindow(binding.stickerEditTextView)
                }

                MotionEvent.ACTION_MOVE -> {
                    binding.flTopMask.visibility = VISIBLE
                    binding.vBottomMask.visibility = VISIBLE
                    binding.vCropHorizontalLineTop.visibility = VISIBLE
                    binding.vCropHorizontalLineBottom.visibility = VISIBLE
                    binding.vCropVerticalLineStart.visibility = VISIBLE
                    binding.vCropVerticalLineEnd.visibility = VISIBLE
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    binding.flTopMask.visibility = INVISIBLE
                    binding.vBottomMask.visibility = INVISIBLE
                    binding.vCropHorizontalLineTop.visibility = INVISIBLE
                    binding.vCropHorizontalLineBottom.visibility = INVISIBLE
                    binding.vCropVerticalLineStart.visibility = INVISIBLE
                    binding.vCropVerticalLineEnd.visibility = INVISIBLE
                    if (!binding.stickerTextFrameLayout.del) {
                        myListItem?.run {
                            chartletSave(this)
                        }
                    }
                }
            }
        }
        binding.stickerTextFrameLayout.onClick {
            if (!binding.stickerTextFrameLayout.drag && CommonApp.liveBridge?.inAnchor(
                    PreferenceManager.Companion.instance.userId.toString()
                ) == true
            ) {
                OtherUtils.showSoftInputFromWindow(binding.stickerEditTextView)
            }
        }
        binding.stickerEditTextView.onClick {
            if (CommonApp.liveBridge?.inAnchor(PreferenceManager.Companion.instance.userId.toString()) == false) {
                launch {
                    delay(15000)
                    binding.ivStickerClose.visibility = GONE
                }
                binding.ivStickerClose.visibility = VISIBLE
            }
        }
        binding.ivStickerClose.onClick {
            closeSticker = true
            binding.ivStickerClose.visibility = GONE
            removeSticker()
        }
    }

    /**
     * 设置贴纸LayoutParams
     */
    private fun setStickerLayoutParams(listItem: ListItem) {
        if (listItem.location_x != null && listItem.location_y != null) {
            val layoutParams = LayoutParams(
                LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT
            )
            val locationX = listItem.location_x!!.toFloat()
            val locationY = listItem.location_y!!.toFloat()
            val marginStart = if (locationX > 1) {
                locationX.toInt()
            } else {
                (locationX * ScreenUtil.getFoldWidth()).toInt()
            }
            val topMargin = if (locationY > 1) {
                locationY.toInt()
            } else {
                (locationY * ScreenUtil.getScreenHeight()).toInt()
            }
            if ((marginStart + binding.stickerTextFrameLayout.width) > ScreenUtil.getFoldWidth()) {
                layoutParams.endToEnd = ConstraintSet.PARENT_ID
            } else {
                layoutParams.startToStart = ConstraintSet.PARENT_ID
                layoutParams.marginStart = marginStart
            }
            if ((topMargin + binding.stickerTextFrameLayout.height) > (ScreenUtil.getScreenHeight() / 3 * 2)) {
                layoutParams.bottomToBottom = ConstraintSet.PARENT_ID
                layoutParams.bottomMargin =
                    (ScreenUtil.getScreenHeight() / 3) + ScreenUtil.getNavBarHeight() + ScreenUtil.dp2px(
                        10
                    )
            } else {
                layoutParams.topToTop = ConstraintSet.PARENT_ID
                layoutParams.topMargin = topMargin
            }
            binding.stickerTextFrameLayout.layoutParams = layoutParams
        } else {
            val layoutParams = LayoutParams(
                LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT
            )
            layoutParams.topToTop = ConstraintSet.PARENT_ID
            layoutParams.bottomToBottom = ConstraintSet.PARENT_ID
            layoutParams.startToStart = ConstraintSet.PARENT_ID
            layoutParams.endToEnd = ConstraintSet.PARENT_ID
            binding.stickerTextFrameLayout.layoutParams = layoutParams
        }
    }


    /**
     * 移除贴纸
     */
    private fun removeSticker() {
        binding.flTopMask.visibility = INVISIBLE
        binding.vBottomMask.visibility = INVISIBLE
        binding.stickerTextFrameLayout.visibility = INVISIBLE
        binding.vCropHorizontalLineTop.visibility = INVISIBLE
        binding.vCropHorizontalLineBottom.visibility = INVISIBLE
        binding.vCropVerticalLineStart.visibility = INVISIBLE
        binding.vCropVerticalLineEnd.visibility = INVISIBLE
    }


    /**
     * 保存贴纸
     */
    private fun chartletSave(listItem: ListItem) {
        var word = binding.stickerEditTextView.text.toString()
        if (TranslateResource.getStringResources(listItem.word) == binding.stickerEditTextView.text.toString()) {
            word = listItem.word
        }
        val locationX =
            binding.stickerTextFrameLayout.x / ScreenUtil.getFoldWidth().toFloat()
        val locationY =
            binding.stickerTextFrameLayout.y / ScreenUtil.getScreenHeight().toFloat()
        viewModel?.fetchChartLetSave(
            locationX.toString(),
            locationY.toString(),
            listItem.chartletId,
            word
        )
    }

}