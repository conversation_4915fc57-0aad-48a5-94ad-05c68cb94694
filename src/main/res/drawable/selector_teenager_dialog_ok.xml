<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="100dp" />
            <gradient android:angle="180" android:centerColor="#E6DDC4FF" android:endColor="#E6FFC7EF" android:startColor="#E6B3DDFF" android:type="linear" android:useLevel="true" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="100dp" />
            <gradient android:angle="180" android:centerColor="#ffddc4ff" android:endColor="#ffffc7ef" android:startColor="#ffb3ddff" android:type="linear" android:useLevel="true" />
        </shape>
    </item>
</selector>