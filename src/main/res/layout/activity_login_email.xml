<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    tools:background="@color/blue"
    tools:ignore="SpUsage,LabelFor,ContentDescription">

    <TextView
        android:id="@+id/tv_hello"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="32dp"
        android:textColor="@color/black"
        android:textSize="22dp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage"
        tools:text="使用邮箱登录" />

    <com.androidtool.common.widget.ClearEditText
        android:id="@+id/phoneEdit"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="38dp"
        android:autofillHints="phoneNumber"
        android:background="@drawable/shape_bg_login_input"
        android:drawableEnd="@drawable/icon_edit_clear"
        android:drawablePadding="5dp"
        android:ellipsize="end"
        android:hint="@string/login_input_phone_num"
        android:imeOptions="actionNext"
        android:inputType="textEmailAddress"
        android:paddingHorizontal="16dp"
        android:singleLine="true"
        android:textAlignment="viewStart"
        android:textColor="#343434"
        android:textColorHint="#D9D9DD"
        android:textSize="14dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_hello" />

    <TextView
        android:id="@+id/nextButton"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_marginStart="54dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="54dp"
        android:background="@drawable/selector_btn_purple_5259f7_r30"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/next_step"
        android:textColor="@color/button_text_color_white"
        android:textSize="16dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/phoneEdit" />

    <TextView
        android:id="@+id/agreementHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="start|center"
        android:text="@string/agreement_hint"
        android:textColor="#666666"
        android:textSize="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/nextButton" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/agreementLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/agreementHint">

        <TextView
            android:id="@+id/userAgreement"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            android:paddingStart="4dp"
            android:paddingTop="2dp"
            android:paddingEnd="4dp"
            android:paddingBottom="2dp"
            android:text="@string/user_agreement"
            android:textColor="@color/main_color_purple"
            android:textSize="10dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/and"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:text="@string/and"
            android:textColor="#666666"
            android:textSize="10dp"
            app:layout_constraintStart_toEndOf="@id/userAgreement"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/privacyAgreement"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            android:paddingStart="4dp"
            android:paddingTop="2dp"
            android:paddingEnd="0dp"
            android:paddingBottom="2dp"
            android:text="@string/privacy_agreement"
            android:textColor="@color/main_color_purple"
            android:textSize="10dp"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/and"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>