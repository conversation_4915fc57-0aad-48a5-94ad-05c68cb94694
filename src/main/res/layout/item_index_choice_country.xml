<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="28dp"
    tools:ignore="MissingConstraints">

    <LinearLayout
        android:id="@+id/fatherCl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/choice_country_uncheck"
        android:gravity="center"
        android:minWidth="38dp"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivCheckedCountry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="14dp"
            android:minHeight="14dp"
            tools:background="@color/blue"
            tools:layout_height="14dp"
            tools:layout_width="14dp"
            tools:visibility="visible" />
    </LinearLayout>
    <View
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/fatherCl"
        android:layout_width="8dp"
        android:layout_height="1dp"/>


</androidx.constraintlayout.widget.ConstraintLayout>