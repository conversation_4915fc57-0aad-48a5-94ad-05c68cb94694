<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:ignore="SpUsage">

    <com.androidtool.common.widget.network.NoNetworkTipsView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="15dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/activityTitle"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:ellipsize="end"
        android:gravity="center"
        android:paddingStart="48dp"
        android:paddingEnd="48dp"
        android:singleLine="true"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/backButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:clickable="true"
        android:contentDescription="@null"
        android:focusable="true"
        android:padding="8dp"
        android:src="@drawable/icon_back_hei"
        app:layout_constraintBottom_toBottomOf="@id/activityTitle"
        app:layout_constraintStart_toStartOf="@id/activityTitle"
        app:layout_constraintTop_toTopOf="@id/activityTitle" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/extraButtonLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        app:layout_constraintBottom_toBottomOf="@id/activityTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/activityTitle" />

    <FrameLayout
        android:id="@+id/contentLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/activityTitle" />



</androidx.constraintlayout.widget.ConstraintLayout>