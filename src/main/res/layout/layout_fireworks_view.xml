<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="242dp"
    android:paddingStart="14dp"
    android:layout_height="wrap_content"
    tools:ignore="RtlSymmetry">


    <View
        android:layout_width="228dp"
        android:layout_height="26dp"
        android:layout_marginTop="6dp"
        android:background="@drawable/bg_yanhua"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/icon_yanhua"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.androidtool.common.widget.AlwaysMarqueeTextView
        android:id="@+id/tv_content"
        android:layout_width="145dp"
        android:layout_height="26dp"
        android:layout_marginTop="6dp"
        android:layout_marginStart="38dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:singleLine="true"
        android:ellipsize="marquee"
        android:textAlignment="viewStart"
        android:gravity="start|center_vertical"
        android:marqueeRepeatLimit="marquee_forever"
        tools:text="烟花奖励获得3456金币"
        android:textColor="#fffff8e9"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>