<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dp_15"
    android:paddingBottom="@dimen/dp_15"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/accostGiftLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/shape_chat_left_bg_with_border"
        android:paddingStart="10dp"
        android:paddingEnd="20dp">

        <ImageView
            android:id="@+id/giftImg"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_marginStart="@dimen/dp_8"
            android:contentDescription=""
            app:layout_constraintBottom_toBottomOf="@id/accostGiftLayout"
            app:layout_constraintStart_toStartOf="@id/accostGiftLayout"
            app:layout_constraintTop_toTopOf="@id/accostGiftLayout"
            />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline3"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.5" />

        <TextView
            android:id="@+id/giftTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:ellipsize="end"
            android:maxLines="1"
            android:text=""
            android:textColor="#111111"
            android:textSize="10dp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/guideline3"
            app:layout_constraintStart_toEndOf="@id/giftImg"
            tools:text="收到" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_5"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#111111"
            android:textSize="10dp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/guideline3"
            app:layout_constraintStart_toEndOf="@id/giftTitle"
            app:layout_constraintTop_toTopOf="@+id/giftTitle" />

        <TextView
            android:id="@+id/giftName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:ellipsize="end"
            android:maxLines="1"
            android:text=""
            android:textColor="#111111"
            android:textSize="8dp"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/giftImg"
            app:layout_constraintTop_toBottomOf="@id/guideline3"
            tools:text="收到" />

        <TextView
            android:id="@+id/giftNumber"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#111111"
            android:textSize="8dp"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/giftName"
            app:layout_constraintTop_toBottomOf="@id/guideline3"
            tools:text="x1" />

        <TextView
            android:id="@+id/giftPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#111111"
            android:textSize="6dp"
            app:layout_constraintBottom_toBottomOf="@+id/giftNumber"
            app:layout_constraintStart_toEndOf="@id/giftNumber"
            app:layout_constraintTop_toTopOf="@+id/guideline3"
            tools:text="(+2000积分)" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>