<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    android:paddingEnd="16dp">



    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/iv_avatar"
        android:layout_marginStart="15dp"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:roundPercent="1" />

    <ImageView
        android:id="@+id/iv_num"
        android:layout_width="55dp"
        android:layout_height="55dp"
        android:visibility="gone"
        android:layout_marginTop="5dp"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/ic_top1"/>


    <TextView
        android:id="@+id/nickname"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="120dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="6dp"
        android:textColor="@color/black3"
        android:textSize="14sp"
        android:singleLine="true"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        tools:text="可可爱爱可可爱爱可可爱爱可可爱爱可可爱爱可可爱爱" />


    <TextView
        android:id="@+id/tvCountryFlag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:textColor="@color/black"
        app:layout_constraintBottom_toBottomOf="@id/nickname"
        app:layout_constraintStart_toEndOf="@id/nickname"
        app:layout_constraintTop_toTopOf="@id/nickname"
        tools:text="🇺🇸" />


    <com.androidtool.common.widget.GenderAgeView
        android:id="@+id/genderAndAge"
        android:layout_width="wrap_content"
        android:layout_height="14dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="6dp"
        android:drawablePadding="1dp"
        android:gravity="center"
        android:paddingStart="4dp"
        android:paddingEnd="4dp"
        android:textColor="@color/white"
        android:textSize="8dp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toBottomOf="@id/nickname"
        tools:foreground="@color/green_point"
        tools:height="14dp"
        tools:width="28dp" />


    <ImageView
        android:id="@+id/wealthLevel"
        android:layout_width="wrap_content"
        android:layout_height="14dp"
        android:layout_marginStart="4dp"
        android:contentDescription="@null"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="@id/genderAndAge"
        app:layout_constraintStart_toEndOf="@id/genderAndAge"
        app:layout_constraintTop_toTopOf="@id/genderAndAge" />



    <TextView
        android:id="@+id/tv_accept"
        android:layout_width="54dp"
        android:layout_height="24dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/shape_gradient_primary_secondary_corners_100"
        android:gravity="center"
        tools:text="@string/accept"
        android:textColor="@color/white"
        android:textSize="11sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_refuse"
        app:layout_constraintTop_toTopOf="parent" />

    <!--拒绝-->
    <com.noober.background.view.BLTextView
        android:id="@+id/tv_refuse"
        android:layout_width="54dp"
        android:layout_height="24dp"
        android:gravity="center"
        tools:text="@string/refuse"
        android:textColor="@color/main_color_purple"
        android:textSize="11sp"
        app:bl_corners_radius="16dp"
        app:bl_solid_color="#265259F7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



</androidx.constraintlayout.widget.ConstraintLayout>