<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="52dp"
    android:paddingHorizontal="12dp"
    tools:ignore="SpUsage">

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="@color/gray_3"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="送粉丝团礼物" />

    <TextView
        android:id="@+id/tv_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:textColor="#ffaaaaaa"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_name"
        app:layout_constraintStart_toEndOf="@id/tv_name"
        app:layout_constraintTop_toTopOf="@id/tv_name"
        tools:text="(200/300)"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_coin"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_marginTop="4.5dp"
        android:contentDescription="@null"
        android:src="@drawable/icon_coin_10"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <TextView
        android:id="@+id/tv_key"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textColor="#ffaaaaaa"
        android:textSize="10sp"
        app:layout_constraintStart_toEndOf="@id/iv_coin"
        app:layout_constraintTop_toBottomOf="@id/tv_name"
        tools:ignore="SmallSp"
        tools:text="发言一句" />

    <TextView
        android:id="@+id/tv_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:textColor="@color/main_color_purple"
        android:textSize="10sp"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_key"
        app:layout_constraintStart_toEndOf="@id/tv_key"
        tools:ignore="SmallSp"
        tools:text="+5" />

    <TextView
        android:id="@+id/tv_intimacy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:textColor="#ffaaaaaa"
        android:textSize="10sp"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_key"
        app:layout_constraintStart_toEndOf="@id/tv_number"
        tools:ignore="SmallSp"
        tools:text="亲密度" />


    <com.noober.background.view.BLTextView
        android:id="@+id/tv_next"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:paddingHorizontal="18dp"
        android:textColor="@color/main_color_purple"
        android:textSize="12sp"
        android:visibility="gone"
        app:bl_corners_radius="16dp"
        app:bl_solid_color="@color/main_color_purple_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="送礼"
        tools:visibility="gone" />

    <TextView
        android:id="@+id/tv_score_all"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#ffaaaaaa"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="+50"
        tools:visibility="visible" />

    <View
        android:id="@+id/v_line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/gray_e"
        app:layout_constraintBottom_toBottomOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>