<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_edit"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <include
        android:id="@+id/titleBar"
        layout="@layout/title_bar_subpage" />


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:background="@color/white"
                android:orientation="vertical"
                app:layout_constraintTop_toTopOf="parent">


                <EditText
                    android:id="@+id/content"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:background="@color/transparent"
                    android:gravity="start"
                    android:hint="@string/hint_think_sth"
                    android:lineSpacingMultiplier="1.2"
                    android:maxLength="250"
                    android:paddingStart="16dp"
                    android:paddingTop="14dp"
                    android:paddingEnd="16dp"
                    android:textColor="@color/gray_3"
                    android:textColorHint="@color/gray_c"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical|end"
                    android:orientation="horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <TextView
                        android:id="@+id/tvNumber"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textColor="@color/gray_9"
                        android:textSize="12dp"
                        android:textStyle="bold" />


                    <TextView
                        android:id="@+id/tvNumberText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="/250"
                        android:textColor="#999999"
                        android:textSize="12sp" />

                </LinearLayout>


            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="16dp">

                <com.androidrtc.chat.customview.NoScrollGridView
                    android:id="@+id/gridView_dynamic_photo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:horizontalSpacing="8dp"
                    android:numColumns="3"
                    android:verticalSpacing="8dp"
                    android:visibility="gone" />

                <com.noober.background.view.BLImageButton
                    android:id="@+id/iv_add"
                    android:layout_width="104dp"
                    android:layout_height="104dp"
                    android:src="@drawable/dynamic_icon_addpic_unfocused"
                    android:visibility="visible"
                    app:bl_corners_radius="10dp"
                    app:bl_solid_color="#eeeff0" />


                <RelativeLayout
                    android:id="@+id/rl_video"
                    android:layout_width="160dp"
                    android:layout_height="214dp"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <androidx.constraintlayout.utils.widget.ImageFilterView
                        android:id="@+id/iv_video_cover"
                        android:layout_width="160dp"
                        android:layout_height="214dp"
                        android:scaleType="centerCrop"
                        app:round="6dp" />


                    <ImageView
                        android:id="@+id/play_icon"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_centerInParent="true"
                        android:src="@drawable/shouye_zhibo_dabofang" />


                    <ImageView
                        android:id="@+id/iv_video_delete"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:padding="4dp"
                        android:src="@drawable/dynamic_ic_del_img"
                        android:visibility="visible" />
                </RelativeLayout>
            </RelativeLayout>

            <!-- 可见设置，谁可以看 -->
            <LinearLayout
                android:id="@+id/ll_visible_set"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingTop="10dp"
                android:paddingEnd="16dp"
                android:paddingBottom="10dp"
                android:visibility="visible">

                <TextView
                    android:id="@+id/tv_remind"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:drawableStart="@drawable/ic_ta_dayamic"
                    android:drawablePadding="4dp"
                    android:text="提醒谁看"
                    android:textColor="#ff333333"
                    android:textSize="14sp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/ry_remind"
                    android:layout_width="156dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:overScrollMode="never" />


                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_more_dynamic" />

            </LinearLayout>

            <!-- 话题tag -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ll_tag"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingTop="10dp"
                android:paddingEnd="16dp"
                android:paddingBottom="10dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvTopicTag"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    android:drawableStart="@drawable/ic_dynamic_add_tag"
                    android:drawablePadding="4dp"
                    android:gravity="center_vertical"
                    android:textColor="#ff333333"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/ivTopicTagDelete"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UseCompatTextViewDrawableXml"
                    tools:text="推荐话题" />


                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivTopicTagDelete"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_dynamic_add_tag_delete"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.androidtool.common.widget.flowlayout.TagFlowLayout
                android:id="@+id/tagTopic"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp" />
        </LinearLayout>
    </ScrollView>

</LinearLayout>