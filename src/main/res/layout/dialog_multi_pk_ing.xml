<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_corners_radius_top_12_solid_white">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:textColor="@color/gray_3"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="pking" />

    <ImageView
        android:id="@+id/ivBg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:scaleType="fitXY"
        android:src="@drawable/bg_pk_ing"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/ivLeftAvatar1"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="10dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_multi_pk_seat"
        app:layout_constraintBottom_toTopOf="@+id/tvLeftNickname1"
        app:layout_constraintEnd_toStartOf="@id/ivLeftAvatar2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivBg"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:roundPercent="1.0" />

    <ImageView
        android:id="@+id/ivMuteLeft1"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:scaleType="centerInside"
        android:layout_marginStart="25dp"
        android:layout_marginBottom="22dp"
        android:src="@drawable/ic_mute_multi_connect"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/ivLeftAvatar1"
        app:layout_constraintBottom_toBottomOf="@id/ivLeftAvatar1"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvLeftNickname1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:ellipsize="end"
        android:maxWidth="38dp"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivBg"
        app:layout_constraintEnd_toEndOf="@id/ivLeftAvatar1"
        app:layout_constraintStart_toStartOf="@id/ivLeftAvatar1"
        app:layout_constraintTop_toBottomOf="@+id/ivLeftAvatar1"
        tools:text="nicknamenicknamenickname"
        tools:visibility="visible" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/ivLeftAvatar2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="12dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_multi_pk_seat"
        app:layout_constraintBottom_toTopOf="@+id/tvLeftNickname2"
        app:layout_constraintEnd_toStartOf="@id/ivLeftAvatar3"
        app:layout_constraintStart_toEndOf="@+id/ivLeftAvatar1"
        app:layout_constraintTop_toTopOf="@+id/ivBg"
        app:layout_constraintVertical_chainStyle="packed"
        app:roundPercent="1.0" />

    <ImageView
        android:id="@+id/ivMuteLeft2"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:scaleType="centerInside"
        android:layout_marginStart="25dp"
        android:layout_marginBottom="22dp"
        android:src="@drawable/ic_mute_multi_connect"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/ivLeftAvatar2"
        app:layout_constraintBottom_toBottomOf="@id/ivLeftAvatar2"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvLeftNickname2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:ellipsize="end"
        android:maxWidth="38dp"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivBg"
        app:layout_constraintEnd_toEndOf="@id/ivLeftAvatar2"
        app:layout_constraintStart_toStartOf="@id/ivLeftAvatar2"
        app:layout_constraintTop_toBottomOf="@+id/ivLeftAvatar2"
        tools:text="nicknamenicknamenickname"
        tools:visibility="visible" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/ivLeftAvatar3"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="18dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_multi_pk_seat"
        app:layout_constraintBottom_toTopOf="@+id/tvLeftNickname3"
        app:layout_constraintEnd_toStartOf="@+id/vMiddle"
        app:layout_constraintStart_toEndOf="@+id/ivLeftAvatar2"
        app:layout_constraintTop_toTopOf="@+id/ivBg"
        app:layout_constraintVertical_chainStyle="packed"
        app:roundPercent="1.0" />

    <ImageView
        android:id="@+id/ivMuteLeft3"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:scaleType="centerInside"
        android:layout_marginStart="25dp"
        android:layout_marginBottom="22dp"
        android:src="@drawable/ic_mute_multi_connect"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/ivLeftAvatar3"
        app:layout_constraintBottom_toBottomOf="@id/ivLeftAvatar3"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvLeftNickname3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:ellipsize="end"
        android:maxWidth="38dp"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivBg"
        app:layout_constraintEnd_toEndOf="@id/ivLeftAvatar3"
        app:layout_constraintStart_toStartOf="@id/ivLeftAvatar3"
        app:layout_constraintTop_toBottomOf="@+id/ivLeftAvatar3"
        tools:text="nicknamenicknamenickname"
        tools:visibility="visible" />

    <View
        android:id="@+id/vMiddle"
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/ivRightAvatar1"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="10dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_multi_pk_seat"
        app:layout_constraintBottom_toTopOf="@+id/tvRightNickname1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivRightAvatar2"
        app:layout_constraintTop_toTopOf="@+id/ivBg"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:roundPercent="1.0" />

    <ImageView
        android:id="@+id/ivMuteRight1"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:scaleType="centerInside"
        android:layout_marginStart="25dp"
        android:layout_marginBottom="22dp"
        android:src="@drawable/ic_mute_multi_connect"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/ivRightAvatar1"
        app:layout_constraintBottom_toBottomOf="@id/ivRightAvatar1"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvRightNickname1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:ellipsize="end"
        android:maxWidth="38dp"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivBg"
        app:layout_constraintEnd_toEndOf="@id/ivRightAvatar1"
        app:layout_constraintStart_toStartOf="@id/ivRightAvatar1"
        app:layout_constraintTop_toBottomOf="@+id/ivRightAvatar1"
        tools:text="nicknamenicknamenickname"
        tools:visibility="visible" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/ivRightAvatar2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="12dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_multi_pk_seat"
        app:layout_constraintBottom_toTopOf="@+id/tvRightNickname2"
        app:layout_constraintEnd_toStartOf="@id/ivRightAvatar1"
        app:layout_constraintStart_toEndOf="@id/ivRightAvatar3"
        app:layout_constraintTop_toTopOf="@+id/ivBg"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:roundPercent="1.0" />

    <ImageView
        android:id="@+id/ivMuteRight2"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:scaleType="centerInside"
        android:layout_marginStart="25dp"
        android:layout_marginBottom="22dp"
        android:src="@drawable/ic_mute_multi_connect"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/ivRightAvatar2"
        app:layout_constraintBottom_toBottomOf="@id/ivRightAvatar2"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvRightNickname2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:ellipsize="end"
        android:maxWidth="38dp"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivBg"
        app:layout_constraintEnd_toEndOf="@id/ivRightAvatar2"
        app:layout_constraintStart_toStartOf="@id/ivRightAvatar2"
        app:layout_constraintTop_toBottomOf="@+id/ivRightAvatar2"
        tools:text="nicknamenicknamenickname"
        tools:visibility="visible" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/ivRightAvatar3"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="12dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_multi_pk_seat"
        app:layout_constraintBottom_toTopOf="@+id/tvRightNickname3"
        app:layout_constraintEnd_toStartOf="@id/ivRightAvatar2"
        app:layout_constraintStart_toEndOf="@id/vMiddle"
        app:layout_constraintTop_toTopOf="@+id/ivBg"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:roundPercent="1.0" />

    <ImageView
        android:id="@+id/ivMuteRight3"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:scaleType="centerInside"
        android:layout_marginStart="25dp"
        android:layout_marginBottom="22dp"
        android:src="@drawable/ic_mute_multi_connect"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/ivRightAvatar3"
        app:layout_constraintBottom_toBottomOf="@id/ivRightAvatar3"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvRightNickname3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:ellipsize="end"
        android:maxWidth="38dp"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivBg"
        app:layout_constraintEnd_toEndOf="@id/ivRightAvatar3"
        app:layout_constraintStart_toStartOf="@id/ivRightAvatar3"
        app:layout_constraintTop_toBottomOf="@+id/ivRightAvatar3"
        tools:text="nicknamenicknamenickname"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvExit"
        android:layout_width="160dp"
        android:layout_height="42dp"
        android:layout_marginTop="24dp"
        android:background="@drawable/selector_btn_purple_border_5259f7_r30"
        android:gravity="center"
        android:textColor="@color/main_color_purple"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivBg"
        tools:text="退出PK" />

    <View
        android:id="@+id/vBottom"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvExit" />
</androidx.constraintlayout.widget.ConstraintLayout>