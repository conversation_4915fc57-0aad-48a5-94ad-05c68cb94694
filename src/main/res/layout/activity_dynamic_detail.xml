<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/dynamic_detail_bg_color">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:gravity="center"
        android:text="@string/detail"
        android:textColor="@color/dynamic_detail_title_text_color"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/back"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:contentDescription="@null"
        android:padding="12dp"
        android:src="@drawable/icon_back_hei"
        app:layout_constraintBottom_toBottomOf="@id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/title" />

    <com.androidtool.common.widget.CustomSwipeRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never"
                    android:scrollbars="none" />
                <View
                    android:background="@color/windowBackground"
                    android:layout_width="match_parent"
                    android:layout_height="6dp"/>
                <TextView
                    android:id="@+id/tvCount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#ffffff"
                    android:paddingStart="16dp"
                    android:paddingTop="12dp"
                    android:paddingEnd="16dp"
                    android:paddingBottom="10dp"
                    android:textColor="#111111"
                    android:textSize="16dp"
                    android:textStyle="bold"
                    android:visibility="visible"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage"
                    tools:text="评论回复(86）"
                    tools:visibility="visible" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <com.androidtool.common.widget.LoadMoreRecyclerView
                        android:id="@+id/commonRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:overScrollMode="never"
                        android:scrollbars="none" />
                </RelativeLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </com.androidtool.common.widget.CustomSwipeRefreshLayout>
</androidx.constraintlayout.widget.ConstraintLayout>