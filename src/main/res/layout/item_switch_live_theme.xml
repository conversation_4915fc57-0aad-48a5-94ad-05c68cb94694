<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    android:orientation="vertical"
    app:cardCornerRadius="5dp"
    app:cardElevation="0dp">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/fatherCl"
        android:layout_width="match_parent"
        android:layout_height="72dp"
        android:background="@color/blue">

        <TextView
            android:id="@+id/tvThemeName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            android:text="默认主题"
            android:textColor="@color/white"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvThemeDateTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:paddingBottom="10dp"
            android:text="有效期："
            android:textColor="#B3FFFFFF"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="@+id/tvThemeName"
            app:layout_constraintTop_toBottomOf="@+id/tvThemeName" />

        <TextView
            android:id="@+id/tvThemeDate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2021.10.17 - 2022.1017"
            android:textColor="#B3FFFFFF"
            android:textSize="14dp"
            app:layout_constraintStart_toEndOf="@+id/tvThemeDateTitle"
            app:layout_constraintTop_toTopOf="@+id/tvThemeDateTitle" />

        <TextView
            android:id="@+id/tvSwitch"
            android:layout_width="56dp"
            android:layout_height="26dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/bg_white_shape_13"
            android:gravity="center"
            android:text="使用中"
            android:textColor="@color/black"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>