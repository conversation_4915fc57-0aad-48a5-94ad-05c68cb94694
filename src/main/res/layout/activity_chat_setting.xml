<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    tools:ignore="MissingConstraints,SpUsage,UnusedAttribute,RelativeOverlap">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="?attr/selectableItemBackground"
        android:id="@+id/clUser"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingTop="9dp"
        android:paddingEnd="16dp"
        android:paddingBottom="9dp">

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/ivAvatar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:roundPercent="1"
            tools:src="@color/gray_line" />

        <ImageView
            android:id="@+id/ivOnlineStatus"
            android:layout_width="12dp"
            android:layout_height="12dp"

            android:layout_marginEnd="2dp"
            android:layout_marginBottom="2dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
            app:layout_constraintEnd_toEndOf="@+id/ivAvatar"
            tools:src="@drawable/shape_white_border_purple_solid_dot"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivLiving"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:contentDescription="@null"
            android:src="@drawable/ic_state_live"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
            app:layout_constraintEnd_toEndOf="@+id/ivAvatar"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvNickname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/gray_3"
            android:textSize="15dp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivAvatar"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="哇哦记否玩" />

        <TextView
            android:id="@+id/tvCountryFlag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:textColor="@color/black"
            app:layout_constraintBottom_toBottomOf="@+id/tvNickname"
            app:layout_constraintStart_toEndOf="@+id/tvNickname"
            app:layout_constraintTop_toTopOf="@+id/tvNickname"
            tools:text="🇺🇸" />

        <ImageView
            android:id="@+id/ivArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_chat_setting_arrow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@color/gray_f7" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/white"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <TextView
            android:id="@+id/tvTopping"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:textColor="@color/gray_3"
            android:textSize="15dp"
            tools:text="设置为置顶" />

        <ToggleButton
            android:id="@+id/tbTopping"
            android:layout_width="45dp"
            android:layout_height="28dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginTop="2dp"
            android:background="@drawable/selector_switch"
            android:checked="true"
            android:textOff=""
            android:textOn="" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@color/gray_f7" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <TextView
            android:id="@+id/tvLiveNotice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:textColor="@color/gray_3"
            android:textSize="15dp"
            tools:text="开播提醒" />

        <ToggleButton
            android:id="@+id/tbLiveNotice"
            android:layout_width="45dp"
            android:layout_height="28dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginTop="2dp"
            android:background="@drawable/selector_switch"
            android:checked="true"
            android:textOff=""
            android:textOn="" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:background="@color/gray_f7" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <TextView
            android:id="@+id/tvMessageIgnore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:textColor="@color/gray_3"
            android:textSize="15dp"
            tools:text="消息免打扰" />

        <ToggleButton
            android:id="@+id/tbMessageIgnore"
            android:layout_width="45dp"
            android:layout_height="28dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginTop="2dp"
            android:background="@drawable/selector_switch"
            android:checked="true"
            android:textOff=""
            android:textOn="" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@color/gray_f7" />

    <TextView
        android:id="@+id/tvChatClear"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:textColor="@color/gray_3"
        android:textSize="15dp"
        tools:text="清除聊天记录" />

    <View
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@color/gray_f7" />

    <TextView
        android:id="@+id/tvReport"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:textColor="@color/gray_3"
        android:textSize="15dp"
        tools:text="举报" />

    <View
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@color/gray_f7" />

    <TextView
        android:id="@+id/tvAddBlacklist"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:textColor="#FF1444"
        android:textSize="15dp"
        tools:text="加入黑名单" />

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gray_f7" />
</LinearLayout>
