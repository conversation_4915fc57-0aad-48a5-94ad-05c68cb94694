<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:id="@+id/contentLayout"
        android:layout_width="match_parent"
        android:layout_height="320dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/room_private_message_dialog_bg"
        android:orientation="vertical"
        tools:ignore="UselessParent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:id="@+id/ivBack"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:paddingStart="12dp"
                android:paddingLeft="12dp"
                android:paddingEnd="12dp"
                android:paddingRight="12dp"
                android:src="@drawable/icon_back_black"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="返回" />

            <ProgressBar
                android:id="@+id/progressConnecting"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_alignEnd="@+id/title"
                android:layout_marginEnd="4dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/title"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/gray_3"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="消息" />


            <TextView
                android:id="@+id/ignore_unread"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:paddingStart="8dp"
                android:paddingLeft="8dp"
                android:paddingEnd="8dp"
                android:paddingRight="8dp"
                android:textColor="@color/gray_9"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="RelativeOverlap"
                tools:text="清除" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_line" />

        <com.androidrtc.im.ui.widget.IMStatusView
            android:id="@+id/imStatusView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


        <FrameLayout
            android:id="@+id/flChatList"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title" />
    </LinearLayout>
</RelativeLayout>