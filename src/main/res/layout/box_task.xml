<?xml version="1.0" encoding="utf-8"?>
<com.noober.background.view.BLConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_36"
    android:layout_height="@dimen/dp_36"
    app:bl_corners_radius="@dimen/dp_8"
    app:bl_solid_color="#33000000">


    <ImageView
        android:id="@+id/icon"
        android:layout_width="@dimen/dp_31"
        android:layout_height="@dimen/dp_25"
        android:layout_marginTop="@dimen/dp_5"
        android:contentDescription="@null"
        android:src="@drawable/ic_box"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.noober.background.view.BLTextView
        android:id="@+id/tv_time"
        android:layout_width="@dimen/dp_28"
        android:layout_height="@dimen/dp_10"
        android:gravity="center"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="@dimen/dp_8"
        app:bl_corners_radius="@dimen/dp_6"
        app:bl_solid_color="#66000000"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:ignore="SmallSp,SpUsage"
        tools:text="05:33" />

</com.noober.background.view.BLConstraintLayout>