<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="76dp"
    android:paddingHorizontal="16dp">


    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/iv_avatar"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_marginStart="2dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:roundPercent="1" />

    <ImageView
        android:id="@+id/iv_avatar_frame"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar"
        app:layout_constraintStart_toStartOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar" />

    <org.libpag.PAGView
        android:id="@+id/pv_avatar_frame"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar"
        app:layout_constraintStart_toStartOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar" />


    <TextView
        android:id="@+id/nickname"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:maxWidth="200dp"
        android:singleLine="true"
        android:textColor="#333333"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/tvCountryFlag"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@+id/iv_avatar"
        tools:text="可可爱爱可可爱爱可可爱爱可可爱爱可可爱爱可可爱爱" />

    <TextView
        android:id="@+id/tvCountryFlag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        app:layout_constraintBottom_toTopOf="@id/tvRegisterTime"
        app:layout_constraintStart_toStartOf="@id/nickname"
        app:layout_constraintTop_toBottomOf="@id/nickname"
        tools:text="🇺🇸" />

    <TextView
        android:id="@+id/tvUserId"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:textColor="#666666"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@id/tvCountryFlag"
        app:layout_constraintStart_toEndOf="@id/tvCountryFlag"
        app:layout_constraintTop_toTopOf="@id/tvCountryFlag"
        tools:text="ID: 12345678" />

    <TextView
        android:id="@+id/tvRegisterTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#666666"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/iv_avatar"
        app:layout_constraintStart_toStartOf="@id/nickname"
        app:layout_constraintTop_toBottomOf="@id/tvCountryFlag"
        tools:text="注册时间: 2023-12-30" />


    <ImageView
        android:id="@+id/iv_coin"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="3dp"
        android:src="@drawable/icon_vote"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_coin"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_coin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:layout_marginBottom="2dp"
        android:gravity="center"
        android:maxWidth="50dp"
        android:maxLines="1"
        android:textColor="#F4333C"
        android:textSize="14sp"
        android:textStyle="bold"
        app:autoSizeMinTextSize="1sp"
        app:autoSizeStepGranularity="1sp"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toBottomOf="@id/iv_coin"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_coin"
        tools:text="X200" />


</androidx.constraintlayout.widget.ConstraintLayout>