<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:minHeight="114dp">

    <androidx.core.widget.NestedScrollView
        android:layout_width="280dp"
        android:layout_height="420dp"
        android:layout_marginBottom="40dp"
        app:layout_constraintBottom_toTopOf="@+id/constraintLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/ivShareImageUrl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scaleType="fitXY"
            android:visibility="gone"
            app:round="4dp"
            tools:minHeight="420dp"
            tools:src="@color/main_color_purple"
            tools:visibility="visible" />
    </androidx.core.widget.NestedScrollView>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_live_share_bg"
        android:backgroundTint="#FFFFFF"
        android:minHeight="114dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewChatPlatform"
            android:layout_width="match_parent"
            android:layout_height="220dp"
            android:layout_marginHorizontal="18dp"
            android:layout_marginVertical="18dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/vLine"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:spanCount="4"
            tools:itemCount="10"
            tools:listitem="@layout/item_live_share_friend" />


        <View
            android:id="@+id/vLine"
            android:layout_width="match_parent"
            android:layout_height="16dp"
            android:background="@color/windowBackground"
            app:layout_constraintTop_toBottomOf="@+id/recyclerViewChatPlatform" />

        <TextView
            android:id="@+id/tvCancel"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/gray_3"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/recyclerViewChatPlatform" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
