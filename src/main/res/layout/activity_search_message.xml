<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="SpUsage">


    <EditText
        android:id="@+id/et_content"
        android:layout_width="0dp"
        android:layout_height="32dp"
        android:layout_marginStart="16dp"
        android:background="@drawable/search_or_history_bg"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical|start"
        tools:hint="输入联系人或聊天记录"
        android:imeOptions="actionSearch"
        android:inputType="text"
        android:textAlignment="viewStart"
        android:paddingStart="32dp"
        android:paddingEnd="30dp"
        android:textColor="@color/black"
        android:textColorHint="@color/gray_9"
        android:textCursorDrawable="@drawable/shape_edit_cursor"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/searchButton"
        app:layout_constraintEnd_toStartOf="@+id/searchButton"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/searchButton" />


    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:src="@drawable/ic_search"
        android:transitionName="HomeSearchTName"
        app:layout_constraintBottom_toBottomOf="@id/et_content"
        app:layout_constraintStart_toStartOf="@id/et_content"
        app:layout_constraintTop_toTopOf="@id/et_content" />

    <TextView
        android:id="@+id/searchButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:paddingStart="12dp"
        android:paddingTop="16dp"
        android:background="@drawable/selector_click_ripple"
        android:paddingEnd="16dp"
        android:paddingBottom="16dp"
        tools:text="@string/cancel"
        android:textColor="@color/gray_9"
        android:textSize="12dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/et_content"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/clean"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:contentDescription="@null"
        android:focusable="true"
        android:padding="6dp"
        android:scaleType="centerInside"
        android:src="@drawable/ss_delete"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/et_content"
        app:layout_constraintEnd_toEndOf="@id/et_content"
        app:layout_constraintTop_toTopOf="@id/et_content"
        tools:visibility="visible" />


    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/searchButton" />


    <include
        android:id="@+id/llHistory"
        layout="@layout/layout_search_user_history"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_content"
        tools:listitem="@layout/item_search_user" />

</androidx.constraintlayout.widget.ConstraintLayout>