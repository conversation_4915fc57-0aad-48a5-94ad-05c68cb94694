package com.androidtool.common.pop

import androidx.fragment.app.FragmentActivity
import com.androidrtc.common.databinding.DialogCommonTipBinding
import com.androidtool.common.base.BaseBindingDialogFragment
import com.androidtool.common.utils.TranslateResource
import com.androidtool.common.extension.dp

/**
 * @author: mao.ye
 * @updateTime: 2023-09-18
 * @description:公共的Tip弹窗
 */
class CommonTipDialog() : BaseBindingDialogFragment<DialogCommonTipBinding>() {

    private var activity: FragmentActivity? = null
    private var builder: Builder? = null

    // 修改原有构造函数
    constructor(activity: FragmentActivity, builder: Builder) : this() {
        this.activity = activity
        this.builder = builder
    }


    //点击事件监听
    private var actionListener: (() -> Unit)? = null

    //取消监听
    private var cancelListener: (() -> Unit)? = null
    override fun setDialogWidth(): Int {
        return 232.dp //dialog宽度
    }

    override fun initView() {
        binding.ivClose.setOnClickListener {
            if (isAdded) {
                dismissAllowingStateLoss()
            }
            cancelListener?.invoke()
        }
        binding.tvPositive.setOnClickListener {
            if (isAdded) {
                dismissAllowingStateLoss()
            }
            actionListener?.invoke()
        }
        builder?.apply {
            <EMAIL>(title)
            <EMAIL>(positiveButtonText)
            <EMAIL>(message)
            <EMAIL>(actionListener)
            <EMAIL>(cancelListener)
        }
    }

    /**
     * 显示dialog
     */
    fun show() {
        showContext(activity)
    }

    /**
     * 设置标题
     */
    private fun setTitle(value: String): CommonTipDialog {
        if (value.isEmpty()) {
            binding.tvTitle.text = TranslateResource.getStringResources("warm_tips")
        } else {
            binding.tvTitle.text = value
        }
        return this
    }

    /**
     * 设置内容
     */
    private fun setMessage(value: String) {
        binding.tvMessage.text = value
    }

    /**
     * 设置确认按钮文字
     */
    fun setPositiveButtonText(value: String) {
        if (value.isEmpty()) {
            binding.tvPositive.text = TranslateResource.getStringResources("ok")
        } else {
            binding.tvPositive.text = builder?.positiveButtonText ?: ""
        }
    }

    /**
     * 点击事件监听
     */
    private fun setActionListener(listener: (() -> Unit)?) {
        this.actionListener = listener
    }

    /**
     * 取消事件监听
     */
    private fun setCancelListener(listener: (() -> Unit)?) {
        this.cancelListener = listener
    }

    class Builder(private var activity: FragmentActivity) {
        var title: String = ""
        var message: String = ""
        var positiveButtonText: String = ""

        //点击事件监听
        var actionListener: (() -> Unit)? = null

        //取消监听
        var cancelListener: (() -> Unit)? = null


        /**
         * 设置标题
         */
        fun setTitle(value: String): Builder {
            title = value
            return this
        }

        /**
         * 设置内容
         */
        fun setMessage(value: String): Builder {
            message = value
            return this
        }

        /**
         * 设置确认按钮文字
         */
        fun setPositiveButtonText(value: String): Builder {
            this.positiveButtonText = value
            return this
        }

        /**
         * 点击事件监听
         */
        fun setActionListener(listener: (() -> Unit)): Builder {
            this.actionListener = listener
            return this
        }

        /**
         * 取消事件监听
         */
        fun setCancelListener(listener: (() -> Unit)): Builder {
            this.cancelListener = listener
            return this
        }

        fun build(): CommonTipDialog {
            return CommonTipDialog(activity, this)
        }
    }
}