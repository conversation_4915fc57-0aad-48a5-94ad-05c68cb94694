package com.androidtool.common.pop

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import com.androidrtc.common.databinding.FragemntDialogImageBinding
import com.androidtool.CommonApp
import com.androidtool.common.base.BaseBindingDialogFragment
import com.androidtool.common.extension.dp
import com.androidtool.common.extension.onClick
import com.androidtool.common.troll.RetrofitManager
import com.androidtool.common.troll.api.BaseApiService
import com.androidtool.common.utils.AppsFlyerUtils
import com.androidtool.common.utils.ImageLoader
import com.androidtool.common.utils.PressEffectUtil
import com.androidtool.common.utils.TranslateResource
import com.androidtool.common.utils.getLifecycleOwner
import com.androidtool.common.utils.performRequest
import com.model.PopDialogModel


/**
 * @ProjectName: globalAndroid
 * @Package: com.androidrtc.chat.refactor.module.live.widget
 * @ClassName: SignSuccessView
 * @Description: java类作用描述
 * @Author: wangll
 * @CreateDate: 2021/8/4 10:26 上午
 * @UpdateUser: 更新者
 * @UpdateDate: 2021/8/4 10:26 上午
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
class ImageDialogFragment : BaseBindingDialogFragment<FragemntDialogImageBinding>() {
    override fun setCancelable(): Boolean {
        return false
    }

    override fun touchOutCancel(): Boolean {
        return true
    }

    override fun setGravity(): Int {
        return Gravity.CENTER
    }

    override fun getBackgroundTransparency(): Float {
        return 0.4f
    }


    override fun initView() {
    }

    override fun onDestroyView() {
        PressEffectUtil.removePressEffect(binding.tvAction)
        super.onDestroyView()
    }


    private var popDialogModel: PopDialogModel? = null
    private var msgId: String? = null

    @SuppressLint("SetTextI18n")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.let { it ->
            popDialogModel = it.getSerializable("popDialogModel") as PopDialogModel?
            msgId = it.getString("msgId")
        }
        popDialogModel?.let { popDialogModel ->
            if (getBindingValue() != null) {
                if (popDialogModel.key == "withdraw_guidance") {// 提现引导弹窗埋点
                    AppsFlyerUtils.addEvent(AppsFlyerUtils.TXGUIDEPOP_IMP)
                }
                if (popDialogModel.width != 0 && popDialogModel.height != 0) {
                    binding.ivAction.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        this.width = (popDialogModel.width / 3).dp
                        this.height = (popDialogModel.height / 3).dp
                    }
                    ImageLoader.displayImage(
                        binding.ivAction.context,
                        TranslateResource.getStringResources(popDialogModel.popPicture),
                        binding.ivAction,
                        showPlaceHolder = false
                    )
                } else {
                    ImageLoader.loadImage(
                        binding.ivAction.context,
                        TranslateResource.getStringResources(popDialogModel.popPicture)
                    ) { drawable ->
                        drawable?.let {
                            //这里后端返回的应该是3倍图
                            if (getBindingValue() != null) {
                                binding.ivAction.setImageDrawable(it)
                                binding.ivAction.layoutParams.width = (it.intrinsicWidth / 3).dp
                                binding.ivAction.layoutParams.height = (it.intrinsicHeight / 3).dp
                            }
                        }
                    }
                }
                if (popDialogModel.popLable.isNotEmpty()) {
                    setBtnBg()
                    setBtnTextColor()
                    if (popDialogModel.buttonMarginBottom != 0) {
                        binding.tvAction.updateLayoutParams<ConstraintLayout.LayoutParams> {
                            this.bottomMargin = popDialogModel.buttonMarginBottom.dp
                        }
                    }
                    binding.tvAction.visibility = View.VISIBLE
                    binding.tvAction.text =
                        TranslateResource.getStringResources(popDialogModel.popLable)
                    binding.tvAction.onClick {
                        if (popDialogModel.key == "withdraw_guidance") {// 提现引导弹窗埋点
                            AppsFlyerUtils.addEvent(AppsFlyerUtils.TXGUIDEPOP_CLICK)
                        }
                        if (popDialogModel.location.isNotEmpty()) {
                            context?.let { it1 ->
                                CommonApp.getIBridgeListener()?.routeUrl(
                                    it1,
                                    popDialogModel.location
                                )
                                popNotify(popDialogModel.key, "1")
                            }
                        } else {
                            popNotify(popDialogModel.key, "0")
                        }
                        if (isAdded) {
                            dismissAllowingStateLoss()
                        }
                    }
                } else {
                    binding.tvAction.visibility = View.GONE
                }
                binding.ivCancel.onClick {
                    popNotify(popDialogModel.key, "0")
                    dismiss()
                }
            }
        }
        PressEffectUtil.addPressEffect(binding.tvAction)
    }

    /**
     * 按钮背景
     */
    @SuppressLint("UseRequireInsteadOfGet")
    private fun setBtnBg() {
        if (!popDialogModel?.buttonBg.isNullOrEmpty() && context != null) {
            ImageLoader.loadImage(context!!, popDialogModel?.buttonBg!!) {
                // 这里是异步，需要判断布局有没有销毁
                if (it != null && getBindingValue() != null
                    && !popDialogModel?.buttonBg.isNullOrEmpty()
                    && context != null
                ) {
                    binding.tvAction.background = it
                }
            }
        } else if (!popDialogModel?.buttonColor.isNullOrEmpty()) {
            val drawable = GradientDrawable()
            drawable.setColor(Color.parseColor(popDialogModel?.buttonColor))
            drawable.cornerRadius = 100.dp.toFloat()
            binding.tvAction.background = drawable
        }
    }

    /**
     * 按钮文本颜色
     */
    private fun setBtnTextColor() {
        if (!popDialogModel?.textColor.isNullOrEmpty()) {
            binding.tvAction.setTextColor(Color.parseColor(popDialogModel?.textColor))
        }
    }


    private fun popNotify(key: String, action: String) {
        RetrofitManager.getService(BaseApiService::class.java)
            ?.popNotify(
                mapOf(
                    Pair("key", key),
                    Pair("action", action),
                    Pair("msg_id", msgId ?: "")
                )
            )
            ?.performRequest({}, {}, context?.getLifecycleOwner())
    }

    companion object {
        @JvmStatic
        fun newInstance(popDialogModel: PopDialogModel, msgId: String = "") =
            ImageDialogFragment().apply {
                arguments = Bundle().apply {
                    putSerializable("popDialogModel", popDialogModel)
                    putString("msgId", msgId)
                }
            }

    }


}