package com.androidtool.common.extension

import android.animation.ValueAnimator
import android.app.Service
import android.content.res.ColorStateList
import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.updateLayoutParams
import com.androidrtc.common.R

import com.androidtool.common.utils.ScreenUtil
import com.androidtool.common.extension.dp
import com.androidtool.common.utils.resToColor
import com.noober.background.drawable.DrawableCreator

/**
 * author qiang.d
 * date on 2022/9/22
 * description View相关扩展函数
 */

/**
 * 获取输入框文本
 */
fun EditText.string(): String = this.text.toString().trim()
fun TextView.string(): String = this.text.toString().trim()

/**
 * 输入框获取焦点
 */
fun EditText.focus() {
    isFocusable = true
    isFocusableInTouchMode = true
    requestFocus()
    setSelection(text.length)
    val inputManager = context.getSystemService(Service.INPUT_METHOD_SERVICE) as InputMethodManager
    inputManager.showSoftInput(this, InputMethodManager.SHOW_FORCED)
}

/**
 * 修复ViewStatusBar Margin
 */
fun View.fitStatusBar(): View {
    this.updateLayoutParams<MarginLayoutParams> { topMargin = ScreenUtil.getStatusBarHeight() }
    return this
}


/**
 * 隐藏View
 */
fun View.setGone() {
    if (visibility == View.GONE) return
    visibility = View.GONE
}

/**
 * 显示View
 */
fun View.setVisible() {
    if (visibility == View.VISIBLE) return
    visibility = View.VISIBLE
}

fun View.setVisible(isVisible: Boolean) {
    if (isVisible) setVisible() else setGone()
}

/**
 * View 设置 bg Tint
 */
fun View.bgTint(color: String? = null) {
    backgroundTintList =
        if (color.isNullOrEmpty()) null else ColorStateList.valueOf(Color.parseColor(color))
}

/**
 * Image 着色
 */
fun ImageView.tint(color: String?) {
    imageTintList =
        if (color.isNullOrEmpty()) null else ColorStateList.valueOf(Color.parseColor(color))
}

fun View.setOnClickListerWithEffect(listener: View.OnClickListener) {
    setOnClickListener {
        ValueAnimator.ofFloat(1F, 0.9F, 1F).apply {
            addUpdateListener { animator ->
                it.scaleX = animator.animatedValue as Float
                it.scaleY = animator.animatedValue as Float
            }
            duration = 100 // in ms
            start()
        }
        listener.onClick(this)
    }
}

/**
 * 输入框view背景切换
 */
fun EditText.changeParentBg(
    radius: Float = 22f,
    solidColor: String = "#F7F7FC",
    restore: Boolean = false
) {
    val width = if (restore) 0f else if (isFocused) 1f.dp else 0f
    (parent as? ViewGroup)?.background = DrawableCreator.Builder()
        .setStrokeColor(R.color.main_color_purple.resToColor())
        .setSolidColor(Color.parseColor(solidColor))
        .setCornersRadius(radius.dp)
        .setStrokeWidth(width).build()
}







































































































































































































