package com.androidtool.common.provider

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.androidtool.common.troll.RetrofitManager
import com.androidtool.common.troll.api.BaseApiService
import com.androidtool.common.utils.performRequest
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * 接口封装类当基类
 */
open class BaseApiProvider(var lifecycleOwner: LifecycleOwner?) : DefaultLifecycleObserver {
    val dataIsNull = "result is null" // 当数据为空时提示

    init {
        // 初始赋值
        setContext(lifecycleOwner)
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        // 置空，避免内存泄露
        lifecycleOwner = null
    }

    /**
     * 根据上下文获取生命周期
     */
    fun setContext(owner: LifecycleOwner?) {
        owner?.let {
            owner.lifecycle.addObserver(this)
        }
        lifecycleOwner = owner
    }

    /**
     * user/info 通过 infos 字段获取对应信息
     * @param paramMap 请求参数
     * @param onResultCallback 成功回调
     * @param onCallBackError 请求失败或者接口返回空数据回调
     */
    fun getUserInfoByInfos(
        paramMap: Map<String, String>,
        onResultCallback: (String) -> Unit = {},
        onCallBackError: (Throwable) -> Unit = {}
    ) {
        RetrofitManager.getService(BaseApiService::class.java)?.getUserInfoJson(
            paramMap
        )?.performRequest({ result ->
            if (result.data == null) {
                onCallBackError.invoke(Throwable(dataIsNull))
            } else {
                onResultCallback.invoke(result.data!!)
            }
        }, {
            onCallBackError.invoke(it)
        }, lifecycleOwner)
    }


    suspend fun getUserInfoByInfos(
        paramMap: Map<String, String>
    ) = suspendCancellableCoroutine { continuation ->
            getUserInfoByInfos(paramMap, { result ->
                try {
                    continuation.resume(result)
                } catch (e: Exception) {
                    continuation.resumeWithException(e)
                }
            }, { error ->
                continuation.resumeWithException(error)
            })
        }


}