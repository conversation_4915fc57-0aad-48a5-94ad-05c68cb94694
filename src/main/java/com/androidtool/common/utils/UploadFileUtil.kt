package com.androidtool.common.utils

import android.media.MediaExtractor
import android.media.MediaFormat
import com.androidtool.common.net.ProgressRequestBody
import com.androidtool.common.net.UploadPurpose
import com.androidtool.common.troll.BaseResponse
import com.androidtool.common.troll.RetrofitManager
import com.androidtool.common.troll.ServiceErrorController
import com.androidtool.common.troll.api.BaseApiService
import com.androidtool.common.troll.exception.ApiException
import com.google.common.io.Files
import com.google.gson.annotations.SerializedName
import com.model.BaseResponseMode
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.MultipartBody
import java.io.File

/**
 * 上传文件工具类
 * 注意：如果上传图片，先对图片进行压缩处理，原则上单张图片大小控制在1M。
 *      如有特殊需求需写明注释
 */
object UploadFileUtil {
    private var isOss = false // 临时变量

    // 全局上传文件的 scope
    private val upLoadScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // 上传接口数据转换为BaseResponse<T>? 转 T?
    private fun <T> BaseResponse<T>?.asData(): T {
        if (this == null) {
            throw ApiException("ApiService is null")
        }
        if (this.data == null) {
            throw ApiException("Response data is null")
        }
        if (ServiceErrorController.isApiErrorStatus(this.code, this.status)) {
            throw ApiException(this.code.toString(), this.message, null)
        }
        return this.data!!
    }

    /**
     * 视频上传,获取视频编码格式
     */
    private fun videoAddCodedParam(
        path: String,
        purpose: UploadPurpose = UploadPurpose.Temp
    ): String {
        return if (isVideoFile(path) || purpose.value == UploadPurpose.Video.value) {
            // 获取视频编码格式
            runCatching {
                val extractor = MediaExtractor()
                extractor.setDataSource(path)
                var mime = ""
                for (i in 0 until extractor.trackCount) {
                    mime = extractor.getTrackFormat(i).getString(MediaFormat.KEY_MIME) ?: ""
                    if (mime.startsWith("video/")) break
                }
                extractor.release()
                mime.substringAfter("/")
            }.getOrDefault("")
        } else ""
    }

    /**
     * 判断文件类型,是否是视频文件
     */
    private fun isVideoFile(path: String): Boolean {
        // 常见的视频格式的扩展名
        val videoExtensions = arrayOf("mp4", "mov", "avi", "mkv")
        // 文件地址扩展名
        val fileExtension = Files.getFileExtension(path)
        // URLConnection.guessContentTypeFromName(path)
        return videoExtensions.any { it.equals(fileExtension, ignoreCase = true) }
    }

    fun uploadFile(
        file: File,
        purpose: UploadPurpose,
        stateListener: UploadStateListener,
        needVCodec: Boolean = false
    ) = uploadFile(file.absolutePath, purpose, stateListener, needVCodec)

    /**
     * 接口：
     * upload/pre-sign
     * 参数：
     * purpose  场景 / 目的， 目前可选：  log（日志） temp （临时文件）  avatar（头像） video（视频）  image（其他普通图片）
     * type  文件扩展名， 可选参数 ，比如 mp4 或者 png
     * content_type  文件header
     *
     * 接口返回：
     * url   上传用URL ， 传文件具体请求后面详细描述。
     * file_path  文件上传后的路径
     * headers: 需要在 url 中添加的 header
     *
     * needVCodec:是否需要获得视频的编码格式，活动视频上传的时候需要带这个参数
     * @return 返回上传文件Job,外部自行维护取消上传
     */
    fun uploadFile(
        path: String,
        purpose: UploadPurpose,
        stateListener: UploadStateListener,
        needVCodec: Boolean = false
    ): Job {
        return if (isOss) {
            uploadToOssDirect(path, purpose, stateListener, needVCodec)
        } else {
            uploadToAppServer(listOf(path), purpose, stateListener, needVCodec)
        }
    }

    fun uploadFiles(
        paths: List<String>,
        purpose: UploadPurpose,
        totalUploadListener: UploadStateListener
    ): List<Job> {
        return buildList {
            if (isOss) {
                addAll(uploadToOssDirectFiles(paths, purpose, totalUploadListener))
            } else {
                add(uploadToAppServer(paths, purpose, totalUploadListener))
            }
        }
    }

    /**
     * oss直传: 分为两个接口
     * 接口1: 上传文件预处理
     * 接口2: oss直传
     */
    private fun uploadToOssDirect(
        path: String,
        purpose: UploadPurpose,
        stateListener: UploadStateListener?,
        needVCodec: Boolean
    ): Job {
        val apiService = RetrofitManager.getService<BaseApiService>()
        return upLoadScope.launch {
            val contentType = FileUtils.getMimeType(path)
            val params = mapOf(
                Pair("purpose", purpose.value),
                Pair("type", FileUtils.getExtensionName(path)),
                Pair("content_type", contentType)
            )
            // 接口1:上传文件预处理
            kotlin.runCatching { apiService?.uploadFilePostPolicy(params).asData() }
                .onSuccess { data ->
                    val partList = withContext(Dispatchers.Main) {
                        VLog.d(
                            "UploadFile",
                            "uploadFilePostPolicy onSuccess=${data.filePath} ${data.host}"
                        )
                        val file = File(path)
                        val progressBody = ProgressRequestBody(file, contentType) { percentage ->
                            VLog.d("UploadFile", "oss直传实时进度,path=$path,Progress=$percentage")
                            stateListener?.onUploadState(
                                UploadState.OnProgress(path, percentage)
                            )
                        }
                        buildList {
                            data.form?.forEach {
                                add(MultipartBody.Part.createFormData(it.key, it.value))
                            }
                            add(MultipartBody.Part.createFormData("file", file.name, progressBody))
                        }
                    }

                    // 接口2:oss直传
                    val vcodec = if (needVCodec) videoAddCodedParam(path, purpose) else ""
                    runCatching {
                        val ossResult = apiService?.uploadFileNew(data.host, partList)
                        withContext(Dispatchers.Main) {
                            VLog.d("UploadFile", "oss直传 onSuccess=$ossResult")
                            if (ossResult == "FileAlreadyExists") {
                                stateListener?.onUploadState(
                                    UploadState.Success(
                                        path,
                                        data.filePath.toString(),
                                        vcodec
                                    )
                                )
                            } else {
                                stateListener?.onUploadState(
                                    UploadState.Error(path, ossResult.toString())
                                )
                            }
                        }
                    }.onFailure {
                        withContext(Dispatchers.Main) {
                            val apiException = it as? ApiException
                            VLog.d("UploadFile", "oss直传 onFailure,errorMsg=${apiException?.errorMsg}")
                            if (apiException?.errorMsg == "FileAlreadyExists" || apiException?.errorMsg == "Response Empty") {
                                VLog.d("UploadFile", "oss直传 success:${data.filePath}")
                                stateListener?.onUploadState(
                                    UploadState.Success(
                                        path,
                                        data.filePath ?: "",
                                        vcodec
                                    )
                                )
                            } else {
                                stateListener?.onUploadState(
                                    UploadState.Error(
                                        path,
                                        apiException?.errorMsg.toString()
                                    )
                                )
                            }
                        }
                    }
                }
                .onFailure {
                    withContext(Dispatchers.Main) {
                        VLog.d("UploadFile", "uploadFilePostPolicy onFailure=${it.message}")
                        stateListener?.onUploadState(UploadState.Error(path, it.message))
                    }
                }
        }
    }

    /**
     * oss直传: 批量文件
     */
    private fun uploadToOssDirectFiles(
        paths: List<String>,
        purpose: UploadPurpose,
        totalUploadListener: UploadStateListener,
    ): List<Job> {
        val eachFileListeners = handerBatchUploadProgress(paths, totalUploadListener)
        return paths.map {
            uploadToOssDirect(it, purpose, eachFileListeners[it], purpose == UploadPurpose.Video)
        }
    }

    /**
     * App服务器上传: 区分临时文件
     */
    private fun uploadToAppServer(
        paths: List<String>,
        purpose: UploadPurpose,
        totalUploadListener: UploadStateListener,
        needVCodec: Boolean = false
    ): Job {
        val partList = arrayListOf<MultipartBody.Part>()
        val uploadProgress = hashMapOf<String, Boolean>()
        paths.forEach {
            val file = File(it)
            if (file.exists()) {
                VLog.e("ada", "add File size:${file.length()} $it")
                val progressBody = ProgressRequestBody(file, "*/*") { percentage ->
                    if (paths.size == 1) {
                        // 单个文件上传进度监听
                        VLog.d("UploadFile", "服务器:单个文件上传进度,path=$it , percentage=$percentage")
                        totalUploadListener.onUploadState(UploadState.OnProgress(it, percentage))
                    } else {
                        // 批量上传文件进度
                        if (uploadProgress[it] != true && percentage == 100) {
                            uploadProgress[it] = true
                            val successCount = uploadProgress.filter { entry -> entry.value }.size
                            val newPercentage = successCount * 100 / paths.size
                            AsyncTaskUtil.executeMainIntensiveTask {
                                VLog.d("UploadFile", "服务器:批量上传,path=$it , newPercentage=$newPercentage")
                                totalUploadListener.onUploadState(UploadState.OnProgress(it, newPercentage))
                            }
                        }
                    }
                }
                partList.add(
                    MultipartBody.Part.createFormData("upload_file[]", file.name, progressBody)
                )
            }
        }
        return upLoadScope.launch {
            // 视频编码格式---(不管是否批量上传,只取第一个文件)
            val vcodec = if (needVCodec) videoAddCodedParam(paths.firstOrNull() ?: "", purpose) else ""

            // 请求
            val apiService = RetrofitManager.getService<BaseApiService>()
            kotlin.runCatching {
                val request = if (purpose.value == UploadPurpose.Temp.value) {
                    apiService?.uploadTmpFile(partList) // 临时文件
                } else {
                    apiService?.uploadFile(partList)
                }
                request.asData()
            }
                .onSuccess {
                    withContext(Dispatchers.Main) {
                        if (it.isNotEmpty()) {
                            totalUploadListener.onUploadState(
                                UploadState.Success(
                                    source = paths.toString(),
                                    targetPath = it.firstOrNull() ?: "",
                                    targetPaths = it,
                                    vCodec = vcodec
                                )
                            )
                        } else {
                            totalUploadListener.onUploadState(UploadState.Error(paths.toString(), "Path is empty"))
                        }
                    }
                }
                .onFailure {
                    withContext(Dispatchers.Main) {
                        VLog.d("UploadFile", "服务器上传,path=$paths,onFailure:${it.message}")
                        totalUploadListener.onUploadState(UploadState.Error(paths.toString(), it.message))
                    }
                }
        }
    }

    /**
     * 处理批量上传文件的进度
     */
    private fun handerBatchUploadProgress(
        paths: List<String>,
        totalUploadListener: UploadStateListener
    ): Map<String, UploadStateListener> {
        val tempUploadListeners = hashMapOf<String, UploadStateListener>()

        // 监听每一个文件的上传结果的状态
        val stateListenerMap: HashMap<String, UploadState> = HashMap()
        paths.forEach {
            val listener = UploadStateListener { state ->
                val path = state.originPath
                when (state) {
                    is UploadState.Success -> {
                        stateListenerMap[path] = state

                        val successCount = stateListenerMap.filter { entry ->
                            entry.value is UploadState.Success
                        }.size
                        val percent = successCount * 100 / paths.size
                        // VLog.d("UploadFile", "批量上传的实时进度,Progress=$percent")
                        totalUploadListener.onUploadState(
                            UploadState.OnProgress(paths.toString(), percent)
                        )
                        if (percent == 100) {
                            val targetPaths = ArrayList<String>()
                            for (tempPath in paths) {
                                val eachState = stateListenerMap[tempPath]
                                if (eachState is UploadState.Success) {
                                    targetPaths.add(eachState.targetPath)
                                }
                            }

                            VLog.d("UploadFile", "批量上传成功,targetPaths=$targetPaths")
                            totalUploadListener.onUploadState(
                                UploadState.Success(
                                    source = paths.toString(),
                                    targetPaths = targetPaths,
                                    vCodec = state.vCodec
                                )
                            )
                        }
                    }

                    is UploadState.Error -> {
                        // 只要有一个错误，那么就全错了
                        totalUploadListener.onUploadState(state)
                    }

                    is UploadState.OnProgress -> {}
                    is UploadState.Idle -> {}
                }
            }
            tempUploadListeners[it] = listener
            // 保存所有文件的上传状态
            stateListenerMap[it] = UploadState.Idle(it)
        }
        return tempUploadListeners
    }

    //=================================================
    //=================================================
    //=================================================
    //=================================================

    data class UploadPostPolicy(
        val host: String = "", // 上传用url host
        @SerializedName("file_path")
        val filePath: String? = "", // 文件上传后的路径
        val form: Map<String, String>? = null,
    ) : BaseResponseMode()

    fun interface UploadStateListener {
        fun onUploadState(state: UploadState)
    }

    sealed class UploadState(val status: Int, val originPath: String) {
        data class Idle(val source: String) : UploadState(0, source)
        data class OnProgress(val source: String, val percentage: Int) : UploadState(1, source)
        data class Success(
            val source: String, val targetPath: String = "", val vCodec: String = "",
            val targetPaths: List<String> = listOf() // 用于回调批量上传图片的目标地址
        ) : UploadState(2, source)

        data class Error(val source: String, val message: String?) : UploadState(3, source)
    }
}

