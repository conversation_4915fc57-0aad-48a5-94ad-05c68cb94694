package com.androidtool.common.widget

import android.content.Context
import android.content.res.TypedArray
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.graphics.toRect
import com.androidrtc.common.R

class GridPasswordEditText @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : AppCompatEditText(context, attrs) {

    private var mGridCount = 6 // 默认6个方格
    private var mGridSpacing: Float = 10f// 方格间距
    private var mGridItemWidth: Float = 40f //每个方块的宽度
    private lateinit var mGridPaint: Paint
    private lateinit var mTextPaint: Paint
    private lateinit var mGridRects: Array<RectF>
    private var mBackgroundDrawableFocus: Drawable? = null
    private var mBackgroundDrawableEnabled: Drawable? = null

    private var mBackgroundDrawableNormal: Drawable? = null

    init {
        val a: TypedArray = context.obtainStyledAttributes(attrs, R.styleable.GridPasswordEditText)
        mGridCount = a.getInt(R.styleable.GridPasswordEditText_gridCount, 6)
        mGridSpacing = a.getDimension(R.styleable.GridPasswordEditText_gridSpacing, 10f)
        mGridItemWidth = a.getDimension(R.styleable.GridPasswordEditText_gridItemWidth, 40f)
        a.recycle()
        maxEms = mGridCount
        init()
    }

    private fun init() {
        mGridPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            style = Paint.Style.STROKE
            color = Color.GRAY
        }

        mTextPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            textSize = <EMAIL>
            typeface = <EMAIL>
            color = currentTextColor
            textAlign = Paint.Align.CENTER
        }

        mGridRects = Array(mGridCount) { RectF() }
        // 加载背景资源
        mBackgroundDrawableFocus =
            AppCompatResources.getDrawable(context, R.drawable.shape_et_focused)
        mBackgroundDrawableNormal =
            AppCompatResources.getDrawable(context, R.drawable.shape_et_normal)
        mBackgroundDrawableEnabled =
            AppCompatResources.getDrawable(context, R.drawable.shape_et_enabled)

        // 禁用系统默认的文本绘制
        background = null
        isCursorVisible = false
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                // 限制输入长度
                s?.let {
                    if (it.length > mGridCount) {
                        setText(it.subSequence(0, mGridCount))
                        setSelection(mGridCount)
                    }
                    invalidate() // 触发重绘
                }
            }

            override fun afterTextChanged(s: Editable?) {}
        })
    }


    override fun onDraw(canvas: Canvas) {
        // 绘制已输入的字符
        val text = text.toString()
        val marginX = (width - mGridCount * mGridItemWidth - mGridSpacing * (mGridCount - 1)) / 2
        // 绘制方格
        for (i in 0 until mGridCount) {
            val startX = i * mGridItemWidth + mGridSpacing * i + marginX
            val endX = startX + mGridItemWidth

            mGridRects[i].set(startX.toFloat(), 0f, endX.toFloat(), height.toFloat())

            val drawable = if (i <= text.length) {
                if (isFocused && i == text.length) {
                    mBackgroundDrawableFocus ?: return
                } else {
                    mBackgroundDrawableEnabled ?: return
                }
            } else {
                mBackgroundDrawableNormal ?: return
            }
            drawable.bounds = mGridRects[i].toRect()
            drawable.draw(canvas)
        }

        for (i in text.indices) {
            if (i < mGridCount) {
                val xStart = mGridRects[i].centerX()
                val fontMetrics = mTextPaint.fontMetrics
                val baseline = mGridRects[i].centerY() - (fontMetrics.top + fontMetrics.bottom) / 2
                canvas.drawText(text[i].toString(), xStart, baseline, mTextPaint)
            }
        }
    }
}
