package com.androidrtc.im.ui.chat.viewholder

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.androidrtc.im.Login
import com.androidrtc.im.R
import com.androidrtc.im.databinding.MessageItemBinding
import com.model.UIMessage
import com.androidrtc.im.vo.Direct
import com.androidtool.common.utils.ImageLoader
import com.androidtool.common.utils.TranslateResource
import com.androidtool.common.utils.resToColor


/**
 * 视频通话（旧的，已不使用，但是可能存在旧的消息，可能缺少参数，统一固定样式显示）
 */
class ChatViewHolder(parent: ViewGroup, messageLongClickCallBack: ((View, UIMessage, Int) -> Unit?)?) :
    BaseMessageViewHolder(
        parent,
        MessageItemBinding.inflate(LayoutInflater.from(parent.context), parent, false),
        messageLongClickCallBack
    ) {

    override fun onBind(
        messageList: List<UIMessage>,
        uiMessage: UIMessage,
        position: Int,
        avatar: String,
        leftVipLevel: String,
        rightVipLevel: String,
        isGroupMessage: Boolean
    ) {
        super.onBind(messageList, uiMessage, position, avatar, leftVipLevel, rightVipLevel,isGroupMessage)
        if (uiMessage.uid == Login.uid) {
            setRight()
        } else {
            setLeft()
        }
    }

    override fun onBindPayloads(
        message: UIMessage,
        avatar: String,
        leftVipLevel: String,
        rightVipLevel: String
    ) {
        super.onBindPayloads(message, avatar, leftVipLevel, rightVipLevel)
        if (message.uid == Login.uid) {
            setRightPayloads()
        } else {
            setLeftPayloads()
        }
    }

    override fun setLeft() {
        super.setLeft()
        binding.rightPanel.visibility = View.GONE
        binding.leftPanel.visibility = View.VISIBLE
        binding.leftMessage.setPadding(0, 0, 0, 0)
        setView(binding.leftMessage)
    }

    override fun setRight() {
        super.setRight()
        binding.leftPanel.visibility = View.GONE
        binding.rightPanel.visibility = View.VISIBLE
        ImageLoader.displayImage(binding.rightAvatar.context, Login.avatar, binding.rightAvatar, ImageLoader.LIST_AVATAR_SIZE, ImageLoader.LIST_AVATAR_SIZE)
        binding.rightMessage.setPadding(0, 0, 0, 0)
        setView(binding.rightMessage)
    }

    private fun setView(root: ViewGroup) {
        val inflate = View.inflate(root.context, R.layout.nim_message_item_video_chat_tips, root)
        inflate.setOnClickListener { click() }
        val title = inflate.findViewById<TextView>(R.id.text1)
        title.text = TranslateResource.getStringResources("video_call")
        val fei_intro = inflate.findViewById<TextView>(R.id.text2)
        fei_intro.visibility = View.GONE
        val intro = inflate.findViewById<TextView>(R.id.text)
        intro.text = TranslateResource.getStringResources("click_on_the_video_call")

        val icon = inflate.findViewById<ImageView>(R.id.image)
        val bottomLayout = inflate.findViewById<ConstraintLayout>(R.id.bottomLayout)
        if (message.direct == Direct.RIGHT.value) {
            icon.setImageResource(R.drawable.icon_chat_video_white)
            title.setTextColor(R.color.white.resToColor())
            fei_intro.setTextColor(R.color.yellow_fff799.resToColor())
            intro.setTextColor(R.color.red_FF4D81.resToColor())
            bottomLayout.setBackgroundResource(R.drawable.shape_corners_radius_bottom_10_solid_white)
        } else {
            icon.setImageResource(R.drawable.icon_chat_video_black)
            title.setTextColor(R.color.gray_1.resToColor())
            fei_intro.setTextColor(R.color.red_FF4D81.resToColor())
            intro.setTextColor(R.color.red_FF4D81.resToColor())
            bottomLayout.setBackgroundResource(R.drawable.shape_corners_radius_bottom_10_solid_red_f7ebee)
        }
    }

    private fun click() {
//        val uid = if (message.uid == Login.uid) message.to_uid else message.uid
//        AVChatManager.call(parent.context, uid.toInt(), createdFrom = CreateInType.PM_CHAT.value, createDotFrom = CreateInType.CALL_VIDEO_CHAT.value)
    }
}