package com.androidrtc.chat

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.android.dynamiclibrary.ui.dynamic.add.AddDynamicActivity
import com.android.dynamiclibrary.ui.dynamic.detail.DynamicDetailActivity
import com.android.dynamiclibrary.ui.dynamic.video.detail.DynamicVideoDetailActivity
import com.android.dynamiclibrary.ui.topic.ui.topicdetail.TopicDetailActivity
import com.androidrtc.chat.base.router.NAURLRouter
import com.androidrtc.chat.customview.GiftEffectView
import com.androidrtc.chat.customview.RechargeDialogFragment
import com.androidrtc.chat.http.ErrorCodeHandler
import com.androidrtc.chat.livewidget.dialog.share.dynamic.DynamicShareFragment
import com.androidrtc.chat.livewidget.dialog.share.dynamic.ShareContactsFragment
import com.androidrtc.chat.livewidget.dialog.share.normal.NormalShareFragment
import com.androidrtc.chat.model.LoginParamsBean
import com.androidrtc.chat.model.UseForWhat
import com.androidrtc.chat.modules.SplashActivity
import com.androidrtc.chat.modules.contacts.ContactsActivity
import com.androidrtc.chat.modules.contacts.model.ContactsEnum
import com.androidrtc.chat.modules.dynamic.anonymous.AnonymousDynamicFragment
import com.androidrtc.chat.modules.dynamic.anonymous.homepage.AnonymousUserHomePageActivity
import com.androidrtc.chat.modules.homepage.GiftWallListFragment
import com.androidrtc.chat.modules.homepage.HomepageActivity
import com.androidrtc.chat.modules.homepage.SetSayHelloActivity
import com.androidrtc.chat.modules.login.LocaleSelectorActivity
import com.androidrtc.chat.modules.main.MainActivity
import com.androidrtc.chat.modules.me.ChangePasswordActivity
import com.androidrtc.chat.modules.me.MessageSettingActivity
import com.androidrtc.chat.modules.me.RechargeActivity
import com.androidrtc.chat.modules.me.VideoPlayerDefaultActivity
import com.androidrtc.chat.modules.me.auth.AuthNameActivity
import com.androidrtc.chat.modules.me.auth.RealPersonActivity
import com.androidrtc.chat.modules.me.editprofile.EditProfileActivity
import com.androidrtc.chat.modules.me.vip.GuardActivity
import com.androidrtc.chat.modules.me.vip.VipActivity
import com.androidrtc.chat.modules.news.NoticeActivity
import com.androidrtc.chat.modules.news.recommend.setting.RecommendFriendSettingActivity
import com.androidrtc.chat.modules.safety.phone.SafetyPhoneActivity
import com.androidrtc.chat.modules.search.SearchActivity
import com.androidrtc.chat.modules.search.SearchMessageActivity
import com.androidrtc.im.open.IMHelper
import com.androidrtc.im.repository.MessageRepository
import com.androidrtc.im.ui.chat.ChatActivity
import com.androidrtc.im.ui.group.GroupChatActivity
import com.androidrtc.im.util.PushNoticeView
import com.androidrtc.im.vo.DataType
import com.androidtool.CommonApp
import com.androidtool.common.base.ActivityList
import com.androidtool.common.base.ActivityList.findActivity
import com.androidtool.common.base.ActivityList.getTopActivity
import com.androidtool.common.constants.ActivityRequestCode
import com.androidtool.common.constants.CreateInType
import com.androidtool.common.constants.UserStatus
import com.androidtool.common.data.PreferenceManager
import com.androidtool.common.intface.IRefactorBridge
import com.androidtool.common.net.Apis
import com.androidtool.common.net.uploadlog.CrashStatusLogHelper
import com.androidtool.common.pop.PopLoading
import com.androidtool.common.pop.TaskRewardDialog
import com.androidtool.common.troll.RetrofitManager
import com.androidtool.common.troll.api.BaseApiService
import com.androidtool.common.utils.OtherUtils
import com.androidtool.common.utils.TranslateResource
import com.androidtool.common.utils.VLog
import com.androidtool.common.utils.performRequest
import com.androidtool.common.utils.toIntFix
import com.example.effectplayerlibrary.MusicManager
import com.model.ActivitiesListModel.DatingIcon
import com.model.DynamicShareInfo
import com.model.GiftList
import com.model.RoomShareInfo
import com.model.ShareInfo
import com.model.ShareListBean
import com.model.ShareType
import com.model.TopicSubListBean
import com.model.UserInfo
import com.model.Video
import com.model.VideoActivityShareInfo
import io.rong.imlib.model.Conversation
import org.json.JSONObject

/**
 * 用于与app module桥接通信
 */
class ModuleBridge : IRefactorBridge {

    override fun startMusic(url: String, loop: Int) {
        MusicManager.getInstance().StartMusic(
            url, loop
        )
    }

    override fun toSplash(
        context: Context?,
        identify: String?,
        isGroup: Boolean,
        pushData: String?,
        crashLog: String?,
    ) {
        context?.let {
            try {
                it.startActivity(Intent(context, SplashActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    putExtra("identify", identify)
                    putExtra("isGroup", isGroup)
                    putExtra("pushData", pushData)
                    putExtra("crashLog", crashLog)
                })
            } catch (t: Throwable) {
                t.printStackTrace()
            }
        }
    }

    override fun toContact(context: Context?) {
        context?.let {
            ContactsActivity.jump(it)
        }
    }


    override fun toAuthNameActivity(context: Context) {
        context.startActivity(Intent(context, AuthNameActivity::class.java))
    }

    override fun showErrorCode(context: Context, jsonObject: JSONObject) =
        ErrorCodeHandler.showErrorCode(context, jsonObject)


    override fun toDynamicDetail(context: Context, uid: String, actionType: String) {
        DynamicDetailActivity.jump(context, uid.toIntFix(), actionType)
    }

    override fun toVideoActivity(
        context: Context,
        video: Video,
        width: Int?,
        height: Int?,
        option: Bundle?,
    ) {
        context.startActivity(
            Intent(context, VideoPlayerDefaultActivity::class.java).putExtra("video", video)
                .putExtra("width", width).putExtra("height", height), option
        )
    }

    override fun getGiftEffectView(context: Context) = GiftEffectView(context)

    override fun showGiftEffectView(view: View, gift: GiftList.GiftItem, endCallBack: () -> Unit) {
        if (view is GiftEffectView) {
            view.visibility = View.VISIBLE
            view.setPlayEndEventListener {
                view.visibility = View.GONE
                view.mute()
                endCallBack.invoke()
            }
            view.setData(gift)
            view.showGiftEffect()
        }
    }

    override fun muteGiftEffectView(view: View) {
        if (view is GiftEffectView) {
            view.mute()
        }
    }


    override fun logout() {
        UserHelper.logout()
    }


    override fun toRechargeAct(context: Context, from: String) {
        context.startActivity(Intent(context, RechargeActivity::class.java).putExtra("from", from))
    }


    override fun getUserHelperUser(): UserInfo {
        return UserHelper.getUser()
    }

    override fun isInPerfectInfo(): Boolean {
        val perfectInfoActivity = findActivity("PerfectInfoActivity")
        return !perfectInfoActivity.isEmpty()
    }

    override fun logoutAndRestart(restartMsg: String?) {
        UserHelper.logout()
        ActivityList.exitAppAndRestart(reStartMsg = restartMsg)
    }


    override fun getPrivateLetterFragment(): Fragment {
        return com.androidrtc.im.ui.chatlist.PrivateLetterFragment()
    }


    override fun showRechargeDialog(context: Context) {
        val rechargeDialogFragment = RechargeDialogFragment(context, CreateInType.DATING.value)
        rechargeDialogFragment.setPagerPosition(0)
        rechargeDialogFragment.show()
    }

    override fun clickActivities(activity: Activity, action: DatingIcon) {
        if (action.action != null && !TextUtils.isEmpty(action.action)) {
            NAURLRouter.routeUrl(activity, action.action!!)
        }
    }


    override fun showUserHomePage(
        activity: Activity,
        uid: String,
        roomId: String,
        createIn: String,
        position: Int,
    ) {
        val intent = Intent(activity, HomepageActivity::class.java)
        intent.putExtra("uid", uid)
        intent.putExtra("created_in", createIn)
        intent.putExtra("created_in_id", roomId)
        intent.putExtra("position", position)
        activity.startActivity(intent)
    }

    override fun toFavActivity(context: Context) {
        ContactsActivity.jump(context, ContactsEnum.Fav.CommonFav)
    }

    override fun routeUrl(context: Context, action: String) {
        VLog.e("===routeUrl==action=$action")
        NAURLRouter.routeUrl(context, action)
    }

    override fun notifyActionHandle(intent: Intent, activity: Activity) {
        val notificationAction = intent.getStringExtra("notification_action")
        CrashStatusLogHelper.writerLog("notifyActionHandle $notificationAction")
        if (notificationAction != null) { // 是通知类型
            NAURLRouter.routeUrl(activity, notificationAction) // 跳转制定页面
        } else {
            val identify = intent.getStringExtra("identify")
            if (identify != null) {
                if (identify.trim().isNotEmpty()) {
                    val type = intent.getStringExtra("type")
                    if (DataType.OFFICIAL_ANNOUNCEMENT.value == type) {
                        ChatActivity.toOfficialAnnouncement(activity, identify)
                    } else {
                        // 判断是否是群聊
                        val isGroup = intent.getBooleanExtra("isGroup", false)
                        if (isGroup) {
                            GroupChatActivity.toChat(
                                activity, identify, intent.getStringExtra("groupName") ?: ""
                            )
                        } else {
                            val avatar = intent.getStringExtra("avatar") ?: ""
                            val nickName = intent.getStringExtra("nickname") ?: ""
                            ChatActivity.toChat(activity, identify, nickName, avatar)
                        }
                    }
                }
            }
        }
    }

    override fun closePushNoticeView() { // 关闭自定义应用内消息通知
        PushNoticeView.getInstance().close()
    }

    override fun getScheme() = NAURLRouter.scheme


    //    private fun exitTalk() {
    //        if (LiveRoomManager.isPartyRoom()) {
    //            if (LiveRoomManager.liveStatusLiveData.value == Status.TALK) {
    //                LiveRoomManager.downMic2Server()
    //            }
    //        } else {
    //            if (LiveRoomManager.liveStatusLiveData.value == Status.TALK) {
    //                LiveRoomManager.downMic2Server()
    //            } else if (LiveRoomManager.liveStatusLiveData.value == Status.WANT_TALK) {
    //                LiveRoomManager.cancelWaitMic()
    //            }
    //        }
    //    }

    override fun checkUpdate(context: Context) {
        PopLoading.setText(TranslateResource.getStringResources(R.string.check_tips)).show(context)
        CheckUpdateVersionHelper.checkUpdateManual(context as Activity)
    }

    override fun toBindPhone(context: Context) {
        SafetyPhoneActivity.bindJump(context)
    }

    override fun toDynamicAddActivity(context: Context) {
        context.startActivity(Intent(context, AddDynamicActivity::class.java))
    }

    override fun toEditProfileActivity(context: Context) {
        context.startActivity(Intent(context, EditProfileActivity::class.java))
    }

    override fun toStartAvChat(context: Context) {
        //        AVChatNewActivity.start(context)
    }

    override fun toMatchActivity(context: Context, autoStart: Int) {
        //        context.startActivity(
        //            Intent(context, MatchActivity::class.java).putExtra(
        //                "autoStart",
        //                autoStart
        //            )
        //        )
    }

    override fun webUploadImage(activity: Activity, param: String) {
        if (activity is RechargeActivity) {
            val webRechargeFragment = activity.webFragment
            webRechargeFragment?.uploadImage(param)
        } else if (activity is AppCompatActivity && (activity.supportFragmentManager.findFragmentByTag(
                "RechargeDialogFragment"
            ) is RechargeDialogFragment)
        ) {
            val fragment =
                activity.supportFragmentManager.findFragmentByTag("RechargeDialogFragment") as RechargeDialogFragment?
            if (fragment != null && fragment.webFragment != null) {
                fragment.webFragment.uploadImage(param)
            }
        }
    }

    override fun webUploadVideo(activity: Activity, param: String) {
        if (activity is RechargeActivity) {
            val webRechargeFragment = activity.webFragment
            webRechargeFragment?.uploadVideo()
        } else if (activity is AppCompatActivity && (activity.supportFragmentManager.findFragmentByTag(
                "RechargeDialogFragment"
            ) is RechargeDialogFragment)
        ) {
            val fragment =
                activity.supportFragmentManager.findFragmentByTag("RechargeDialogFragment") as RechargeDialogFragment?
            if (fragment != null && fragment.webFragment != null) {
                fragment.webFragment.uploadVideo()
            }
        }
    }

    override fun webUploadLog(activity: Activity) {
        if (activity is RechargeActivity) {
            val webRechargeFragment = activity.webFragment
            webRechargeFragment?.uploadLog()
        } else if (activity is AppCompatActivity && (activity.supportFragmentManager.findFragmentByTag(
                "RechargeDialogFragment"
            ) is RechargeDialogFragment)
        ) {
            val fragment =
                activity.supportFragmentManager.findFragmentByTag("RechargeDialogFragment") as RechargeDialogFragment?
            if (fragment != null && fragment.webFragment != null) {
                fragment.webFragment.uploadLog()
            }
        }
    }

    override fun showRechargeDialogFragment(context: Context) {
        if (context is FragmentActivity) {
            RechargeDialogFragment(context, CreateInType.RECHARGE.value).show()
        }
    }

    override fun toAutoReplyConfigActivity(context: Context) {
        //        context.startActivity(Intent(context, AutoReplyConfigActivity::class.java))
    }

    override fun selectArea(context: Context) {
        if (OtherUtils.scanForActivity(context) is AppCompatActivity) {
            val activity1 = OtherUtils.scanForActivity(
                context
            ) as AppCompatActivity
            val fragment =
                activity1.supportFragmentManager.findFragmentByTag("RechargeDialogFragment") as RechargeDialogFragment?
            if (fragment != null) {
                fragment.activityResult()
            } else {
                activity1.startActivityForResult(
                    Intent(
                        activity1, LocaleSelectorActivity::class.java
                    ), ActivityRequestCode.TO_LOCALE_SELECTOR_ACTIVITY_REQUEST_CODE
                )
            }
        } else {
            OtherUtils.scanForActivity(context)?.startActivityForResult(
                Intent(
                    OtherUtils.scanForActivity(
                        context
                    ), LocaleSelectorActivity::class.java
                ), ActivityRequestCode.TO_LOCALE_SELECTOR_ACTIVITY_REQUEST_CODE
            )
        }
    }

    override fun toSetSayHelloActivity(context: Context) {
        SetSayHelloActivity.open(context)
    }

    override fun showTaskRewardDialog(context: Context, json: JSONObject) {
        OtherUtils.appCompatActivityValid(context) { appCompatActivity ->
            TaskRewardDialog.show(appCompatActivity.supportFragmentManager, json)
        }
    }

    override fun toMainActivity(context: Context, tab: String) {
        context.startActivity(
            Intent(
                context, MainActivity::class.java
            ).putExtra("tab", tab)
        )
    }

    override fun showInviteBoyRegister(context: Context) {
        NormalShareFragment.to(
            context,
            ShareInfo.Builder().from(ShareType.INVITE_BOY_REGISTER).build(),
        )
    }

    override fun showShare(context: Context, shareUrl: String, shareImgUrl: String?, from: String) {
        NormalShareFragment.to(
            context,
            ShareInfo.Builder().shareUrl(shareUrl).shareImgUrl(shareImgUrl).from(from).build(),
        )
    }

    override fun finishRechargeActivity(context: Context) {

        val activityPay = getTopActivity()
        if (activityPay != null && activityPay is RechargeActivity) {
            activityPay.finish()
        }
    }

    override fun showScoreDialogFragment(activity: AppCompatActivity) {
        //        if (!activity.isFinishing) {
        //            val scoreDialogFragment = ScoreDialogFragment.newInstance()
        //            val dialog = scoreDialogFragment.dialog
        //            if (dialog == null || !dialog.isShowing) {
        //                scoreDialogFragment.show(
        //                    activity.supportFragmentManager, "ScoreDialogFragment"
        //                )
        //            }
        //        }
    }

    override fun showPopMaleDialogFragment(activity: AppCompatActivity, data: String) {

    }

    override fun showNoticeActivity(activity: FragmentActivity?) {
        activity?.startActivity(Intent(activity, NoticeActivity::class.java))
    }

    override fun inChatPage(): Boolean {
        TODO("Not yet implemented")
    }

    override fun showCallWindow(callUserAvatar: String) {

    }

    override fun hideCallWindow() {

    }

    override fun callFromCallWindow(
        callUserId: Int,
        callUserAvatar: String,
        isRandomMatch: Boolean,
        createFrom: String,
        startCallback: (() -> Unit)?,
    ) {
    }

    override fun toVip(context: Context?, selectVip: Int) {
        VipActivity.open(context, selectVip)
    }

    override fun toVip(context: Context?, selectVip: Int, dialogHeight: Int) {
        VipActivity.open(context, selectVip, dialogHeight)
    }

    override fun toGuard(context: Context, dialogHeight: Int, targetUid: String?) {
        GuardActivity.open(context, targetUid = targetUid, dialogHeight = dialogHeight)
    }

    override fun toGuardTarget(context: Context, guardType: Int, targetUid: String) {
        GuardActivity.open(context, guardType, targetUid, true)
    }

    override fun toSelectContacts(
        context: Context,
        shareInfo: ShareInfo,
        selectUser: List<ShareListBean.ShareItem>,
    ) {
        ShareContactsFragment.show(context, shareInfo, selectUser)
    }

    override fun shareWatchLivingMessage(
        userList: List<UserInfo>,
        roomShareInfo: RoomShareInfo,
        comment: String,
    ) {
        MessageRepository.getInstance()
            .sendShareWatchLivingMessage(userList, roomShareInfo, comment = comment)
    }

    override fun shareVideoActivityMessage(
        userList: List<UserInfo>,
        comment: String,
        videoActivityShareInfo: VideoActivityShareInfo,
    ) {
        MessageRepository.getInstance()
            .sendShareVideoActivityMessage(userList, comment = comment, videoActivityShareInfo)
    }

    override fun showVideoActivityShare(
        context: Context,
        videoActivityShareInfo: VideoActivityShareInfo,
    ) {
        val shareInfo = ShareInfo.Builder().shareUrl(videoActivityShareInfo.shareUrl)
            .icon(videoActivityShareInfo.avatar).shareIntro("come_like")
            .extra(videoActivityShareInfo).from(ShareType.VIDEO_ACTIVITY).build()
        DynamicShareFragment.show(context, shareInfo)
    }

    override fun getUserInformationGiftListFragment(uid: String): Fragment {
        return GiftWallListFragment.newInstance(uid, false)
    }

    override fun showDynamicShare(context: Context, dynamicShareInfo: DynamicShareInfo) {
        val shareInfo = ShareInfo.Builder().shareUrl(dynamicShareInfo.h5)
            .icon(dynamicShareInfo.config?.picture ?: "")
            .shareIntro(dynamicShareInfo.config?.shareIntro ?: "").extra(dynamicShareInfo)
            .from(ShareType.DYNAMIC).build()
        DynamicShareFragment.show(context, shareInfo)
    }

    override fun showTopicShare(context: Context, dynamicShareInfo: DynamicShareInfo) {
        val shareInfo = ShareInfo.Builder().shareUrl(dynamicShareInfo.h5)
            .icon(dynamicShareInfo.config?.picture ?: "")
            .shareIntro(dynamicShareInfo.config?.shareIntro ?: "").extra(dynamicShareInfo)
            .from(ShareType.TOPIC).build()
        DynamicShareFragment.show(context, shareInfo)
    }

    override fun showRoomShare(context: Context, roomId: String) {
        val shareInfo =
            ShareInfo.Builder().extra(RoomShareInfo(roomId)).from(ShareType.ROOM).build()
        DynamicShareFragment.show(context, shareInfo, darkMode = true)
    }

    override fun shareDynamicMessage(
        userList: List<UserInfo>,
        comment: String,
        shareInfo: DynamicShareInfo,
    ) {
        MessageRepository.getInstance()
            .sendShareDynamicMessage(userList, comment = comment, shareInfo)
    }

    override fun shareTopicMessage(
        userList: List<UserInfo>,
        comment: String,
        shareInfo: DynamicShareInfo,
    ) {
        MessageRepository.getInstance()
            .sendShareTopicMessage(userList, comment = comment, shareInfo)
    }

    override fun showRoomBottomGameActionDialog() {
    }

    override fun jumpNewMessageSetting() {
        ActivityList.getTopActivity()?.let { context ->
            context.startActivity(Intent(context, MessageSettingActivity::class.java))
        }
    }

    override fun jumpSearchMessageActivity(context: Context) {
        context.startActivity(Intent(context, SearchMessageActivity::class.java))
    }

    override fun toChangePassword(context: Context, hasPasswd: Int) {
        val param = LoginParamsBean(
            hasPasswd = hasPasswd, userid = PreferenceManager.instance.userId.toString()
        )
        ChangePasswordActivity.jump(context, UseForWhat.ChangePassword.Default, param)
    }

    override fun openStartLiveRealFace(context: Context) {
        RealPersonActivity.open(
            context,
            false,
            RealPersonActivity.Companion.CreatedFrom.StartLive.from
        )
    }

    override fun getUnreadMessageCount(
        context: Context,
        conversationId: String,
        isGroup: Boolean,
        callback: (Int) -> Unit,
    ) {
        IMHelper.getConversationUnread(conversationId, callback)
    }

    override fun markUnreadMessage(context: Context, conversationId: String, isGroup: Boolean) {
        IMHelper.clearMessagesUnread(
            conversationId,
            if (isGroup) Conversation.ConversationType.GROUP else Conversation.ConversationType.PRIVATE
        )
    }

    override fun createShortcutIntent(context: Context): Intent {
        return Intent(context, SplashActivity::class.java)
    }

    override fun watchLive(
        context: Context,
        uid: String,
        roomServer: String,
        backRoomId: String,
        backRoomAvatar: String,
    ) {
        val paramMap = mapOf(
            Apis.INFOS to Apis.getApiUserInfo("uid|user_status|in_room"),
            "uid" to uid
        )
        PopLoading.show(context)
        // 先查询这个用户in_room和状态,再决定跳转那个直播间
        RetrofitManager.getService(BaseApiService::class.java)?.getUserInfoJson(paramMap)
            ?.performRequest({
                PopLoading.hide()
                runCatching {
                    val data = JSONObject(it.data ?: "")
                    val inRoom = data.optString("in_room")
                    val userStatus = data.optInt("user_status")
                    var targetRoom = uid
                    when (userStatus) {
                        UserStatus.USER_STATUS_LIVE_SEAT, UserStatus.USER_STATUS_PARTY_SEAT, UserStatus.USER_STATUS_LIVE_ROOM, UserStatus.USER_STATUS_PARTY_ROOM -> {
                            targetRoom = inRoom
                        }
                    }
                    CommonApp.liveBridge?.watchLive(
                        context,
                        targetRoom,
                        roomServer,
                        backRoomId,
                        backRoomAvatar
                    )
                }
            }, { // 查询失败直接跳转个人直播间
                PopLoading.hide()
                CommonApp.liveBridge?.watchLive(
                    context,
                    uid,
                    roomServer,
                    backRoomId,
                    backRoomAvatar
                )
            })

    }

    override fun toRecommendFriendSetting(context: Context?) {
        context?.startActivity(Intent(context, RecommendFriendSettingActivity::class.java))
    }


    override fun toHomePage(context: Context, id: Int) {
        HomepageActivity.toHomePage(context, id)
    }

    override fun toTopicDetail(context: Context?, dynamicTopic: TopicSubListBean.TopicItem) {
        TopicDetailActivity.start(context!!, dynamicTopic.id)
    }

    override fun toAnonymousUserHomePageActivity(context: Context, toString: String) {
        AnonymousUserHomePageActivity.jump(
            context,
            PreferenceManager.instance.userId.toString()
        )
    }

    override fun getAnonymousDynamicPage(): Fragment {
        return AnonymousDynamicFragment()
    }

    override fun toSearchActivity(context: Context?) {
        context?.startActivity(Intent(context, SearchActivity::class.java))
    }

    override fun toDynamicVideoActivity(context: Context, id: Int) {
        DynamicVideoDetailActivity.jumpFromRouter(context, dynamicId = id.toString())
    }
}