package com.androidrtc.chat.customview.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Insets;
import android.graphics.Rect;
import android.os.Build;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.Window;
import android.view.WindowInsets;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.androidrtc.chat.R;
import com.androidrtc.chat.common.emoji.EmojiRecycleView;
import com.androidrtc.chat.common.util.string.StringUtil;
import com.androidtool.common.data.PreferenceManager;
import com.androidtool.common.utils.OtherUtils;
import com.androidtool.common.utils.ScreenUtil;
import com.androidtool.common.utils.TranslateResource;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * Created by Administrator on 2018/4/2.
 * 自定义文本输入框
 * 动态评论使用
 */
public class CustomInputTextDialog extends Dialog {

    private static final int SHOW_LAYOUT_DELAY = 200;//延时毫秒
    private Context mContext;
    private Handler handler;
    private InputMode inputMode = InputMode.NONE;
    private EditText et_input;//文本输入框
    private CheckBox btn_emoticon;//表情按钮
    private LinearLayout btn_send;//发送按钮
    private TextView tvSend;
    private EmojiRecycleView emoticonPickerView;//表情选择框
    private int maxCount = 100;//默认100个汉字
    private OnSendListener onSendListener;
    public static String draft = "";
    private LinearLayout ll_input_view;
    private int screen_height = 0;
    private int status_height = 0;
    private int mLastDiff = 0;
    private boolean isShowEmoji = false;
    private InputMethodManager imm;

    public interface OnSendListener {
        void onSend(CustomInputTextDialog inputTextDialog, CharSequence content);

        void onDismiss();
    }

    private enum InputMode {
        TEXT,
        VOICE,
        EMOTICON,
        MORE,
        VIDEO,
        NONE,
    }

    public CustomInputTextDialog(Context context) {
        super(context, R.style.InputDialog);
        mContext = context;
        setContentView(R.layout.custom_input_text_dialog);
        Window window = getWindow();
        if (window != null) {
            // 设置弹窗全屏
            window.setType(WindowManager.LayoutParams.TYPE_APPLICATION_PANEL);
            window.setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,
                    WindowManager.LayoutParams.FLAG_FULLSCREEN | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN);
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = WindowManager.LayoutParams.MATCH_PARENT;
            window.setAttributes(params);
            window.setGravity(Gravity.BOTTOM);
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        }
        mSoftKeyboardHeight = PreferenceManager.getInstance().getSoftKeyboardHeight();
//        setCancelable(true);
//        setCanceledOnTouchOutside(true);
        init();
        setEmotionLayoutHeight(mSoftKeyboardHeight);
        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                onSendListener.onDismiss();
            }
        });
    }

    private void init() {
        handler = new Handler();
        imm = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
        screen_height = ScreenUtil.INSTANCE.getScreenHeight();
        status_height = ScreenUtil.INSTANCE.getStatusBarHeight();
        ll_input_view = (LinearLayout) findViewById(R.id.ll_input_view);
        et_input = (EditText) findViewById(R.id.et_input);
        btn_emoticon = (CheckBox) findViewById(R.id.btn_emoticon);
        btn_send = (LinearLayout) findViewById(R.id.btn_send);
        emoticonPickerView = findViewById(R.id.emoticon_picker_view);
        tvSend = findViewById(R.id.tvSend);
        findViewById(R.id.rl_outside_view).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        initListener();
        et_input.setHint(TranslateResource.INSTANCE.getStringResources("say_something"));
        tvSend.setText(TranslateResource.INSTANCE.getStringResources("send"));
        if (OtherUtils.INSTANCE.isRtl()) et_input.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
    }

    private void initListener() {
        et_input.addTextChangedListener(new TextWatcher() {
            private int start;
            private int count;
            private int selectionStart;
            private int selectionEnd;
            private int sunCount;
            private boolean flag = false;

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                this.start = start;
                this.count = count;
                selectionStart = et_input.getSelectionStart();
                selectionEnd = et_input.getSelectionEnd();
                if (before != 0 && !flag) {
                    if (before == 1) {
                        if (sunCount == 0) {
                            sunCount = 0;
                        } else {
                            sunCount -= 1;
                        }
                    } else {
                        if (sunCount == 0) {
                            sunCount = 0;
                        } else {
                            sunCount -= 2;
                        }
                    }
                }
                flag = false;
                emoticonPickerView.setEmojiDelBtn(s != null && s.length() > 0);
            }

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                if (et_input.getText().toString().equalsIgnoreCase("")) {
                    sunCount = 0;
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                checkSendButtonEnable(et_input);
                if (s.toString().isEmpty()) {
                    sunCount = 0;
                    start = 0;
                    count = 0;
                    selectionStart = 0;
                    selectionEnd = 0;
                    draft = "";//清空了，保存到草稿
                    return;
                }

                //--------------------------------------------------------------------------------
                int max = maxCount;
                if (sunCount + count <= max) {
                    sunCount += count;
                } else {
                    flag = true;
                    et_input.setSelection(selectionEnd - (count + sunCount - max));
                    s.delete(selectionStart - (count + sunCount - max), selectionEnd);
                    sunCount = max;
                }
                draft = et_input.getText().toString();//保存到草稿
            }
        });

        et_input.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                onSendListener();
                return true;
            }
        });

        et_input.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                updateView(InputMode.TEXT);
                handler.removeCallbacks(showEmojiRunnable);
                emoticonPickerView.setVisibility(View.INVISIBLE);
            }
        });

        btn_emoticon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                updateView(inputMode == InputMode.EMOTICON ? InputMode.TEXT : InputMode.EMOTICON);
            }
        });

        btn_send.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onSendListener();
            }
        });
    }

    /**
     * 点击发送按钮
     */
    private void onSendListener() {
        if (onSendListener != null && et_input != null) {
            String content = et_input.getText().toString().trim();
            // 检测json字符串
            if (OtherUtils.isSendJsonText(content)) {
                et_input.setText("");
                return;
            }
            onSendListener.onSend(this, content);
            draft = "";
            if (!content.isEmpty()) {
                OtherUtils.INSTANCE.hideSoftInputFromWindow(et_input);
                dismiss();
            }
        }
    }

    /**
     * 显示发送或更多
     *
     * @param editText
     */
    private void checkSendButtonEnable(EditText editText) {
        String textMessage = editText.getText().toString();
        if (!TextUtils.isEmpty(StringUtil.removeBlanks(textMessage))) {
            btn_send.setEnabled(true);
        } else {
            btn_send.setEnabled(false);
        }
    }

    private void updateView(InputMode mode) {
        if (mode == inputMode)
            return;
        leavingCurrentState();
        switch (inputMode = mode) {
            case TEXT: {
                switchToTextLayout(true);
            }
            break;
            case EMOTICON: {
                toggleEmojiLayout();
            }
            break;
        }
    }

    private void leavingCurrentState() {
        switch (inputMode) {
            case TEXT: {
                switchToTextLayout(false);
            }
            break;
            case EMOTICON: {
                hideEmojiLayout();
            }
            break;
        }
    }

    // 点击edittext，切换键盘布局
    private void switchToTextLayout(boolean needShowInput) {
        hideEmojiLayout();
        if (needShowInput) {
            handler.postDelayed(showTextRunnable, SHOW_LAYOUT_DELAY);
        } else {
            hideInputMethod();
        }
    }

    // 显示键盘布局
    private void showInputMethod(EditText editTextMessage) {
        hideEmojiLayout();
        editTextMessage.requestLayout();
        editTextMessage.requestFocus();
//        OtherUtil.showSoftInputFromWindow(editTextMessage);
        imm.showSoftInput(editTextMessage, 0);
        btn_emoticon.setChecked(false);
        isShowEmoji = false;
    }

    // 隐藏键盘布局
    private void hideInputMethod() {

        handler.removeCallbacks(showTextRunnable);
//        OtherUtil.hideSoftInputFromWindow(et_input);
        imm.hideSoftInputFromWindow(et_input.getWindowToken(), 0);
//        et_input.clearFocus();
    }

    // 点击表情，切换到表情布局
    private void toggleEmojiLayout() {
        if (emoticonPickerView == null || emoticonPickerView.getVisibility() == View.INVISIBLE) {
            showEmojiLayout();
        } else {
            hideEmojiLayout();
        }
    }

    // 显示表情布局
    private void showEmojiLayout() {
        hideInputMethod();
        handler.postDelayed(showEmojiRunnable, SHOW_LAYOUT_DELAY);
        emoticonPickerView.setClickCallBack(new Function1<String, Unit>() {
            @Override
            public Unit invoke(String s) {
                onEmojiSelected(s);
                return null;
            }
        });
        btn_emoticon.setChecked(true);
        isShowEmoji = true;
    }

    // 隐藏表情布局
    private void hideEmojiLayout() {
        handler.removeCallbacks(showEmojiRunnable);
        if (emoticonPickerView != null) {
            emoticonPickerView.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        // 设置软键盘显示模式
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            getWindow().setDecorFitsSystemWindows(false);
            getWindow().getDecorView().setOnApplyWindowInsetsListener((v, insets) -> {
                Insets ime = insets.getInsets(WindowInsets.Type.ime());
                boolean softVisible = insets.isVisible(WindowInsets.Type.ime());
                if (softVisible && ime.bottom > 0) {
                    onKeyBoardHeightChanged(ime.bottom);
                } else {
                    onKeyBoardHidden();
                    mSoftKeyboardHeight = 0;
                }
                return insets;
            });
        } else {
            getWindow().getDecorView().addOnLayoutChangeListener(onLayoutChangeListener);
        }
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
            getWindow().getDecorView().removeOnLayoutChangeListener(onLayoutChangeListener);
        }
    }

    /**
     * 根布局高度监听，以获取软键盘的高度
     */
    private boolean navigationBarShowing;
    private Rect rect = new Rect();
    private int prevBottom = 0;
    private int mSoftKeyboardHeight;
    private boolean isKeyboardShowed = false; // 是否显示键盘
    private View.OnLayoutChangeListener onLayoutChangeListener = new View.OnLayoutChangeListener() {
        @Override
        public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
            v.getWindowVisibleDisplayFrame(rect);
            if (prevBottom == 0) {
                navigationBarShowing = (ScreenUtil.INSTANCE.getScreenHeight() - ScreenUtil.INSTANCE.getNavBarHeight()) == rect.bottom;
            } else if ((prevBottom - rect.bottom) == ScreenUtil.INSTANCE.getNavBarHeight() && !navigationBarShowing) {
                navigationBarShowing = true;
            } else if ((prevBottom - rect.bottom) == -ScreenUtil.INSTANCE.getNavBarHeight() && navigationBarShowing) {
                navigationBarShowing = false;
            } else {
                if (mSoftKeyboardHeight != 0) {
                    onKeyBoardHeightChanged(prevBottom - rect.bottom);
                }
            }
            prevBottom = rect.bottom;

        }
    };

    /**
     * 软键盘高度更新处理
     */
    private void onKeyBoardHeightChanged(int softKeyboardHeight) {
        if (softKeyboardHeight == 0) {
            return;
        }
        if (softKeyboardHeight > 0) {
            setEmotionLayoutHeight(softKeyboardHeight);
            isKeyboardShowed = true;
            if (softKeyboardHeight != mSoftKeyboardHeight) {
                mSoftKeyboardHeight = softKeyboardHeight;
                PreferenceManager.getInstance().setSoftKeyboardHeight(softKeyboardHeight);
            }
        } else {
            onKeyBoardHidden();
        }
    }

    private void onKeyBoardHidden() {
        // 软键盘还没开启过，不隐藏
        if (!isKeyboardShowed) return;
        // 键盘隐藏
        et_input.clearFocus();
        if (inputMode == InputMode.TEXT) {
            dismiss();
        }
    }

    /**
     * 设置标emoji件高度
     */
    private void setEmotionLayoutHeight(int height) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) emoticonPickerView.getLayoutParams();
        if (layoutParams.height != height) {
            layoutParams.height = height;
            emoticonPickerView.setLayoutParams(layoutParams);
        }
    }

    //----------Runnable-----------
    private Runnable showEmojiRunnable = new Runnable() {
        @Override
        public void run() {
            emoticonPickerView.setVisibility(View.VISIBLE);
        }
    };

    private Runnable showTextRunnable = new Runnable() {
        @Override
        public void run() {
            showInputMethod(et_input);
        }
    };

    //表情选择
    public void onEmojiSelected(String key) {
        Editable mEditable = et_input.getText();
        if (key.equals(EmojiRecycleView.DEL)) {
            et_input.dispatchKeyEvent(new KeyEvent(KeyEvent.ACTION_DOWN, KeyEvent.KEYCODE_DEL));
        } else {
            int start = et_input.getSelectionStart();
            int end = et_input.getSelectionEnd();
            start = (start < 0 ? 0 : start);
            end = (start < 0 ? 0 : end);
            mEditable.replace(start, end, key);
        }
    }

    //点击空白区域，隐藏软键盘
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (isShowing() && shouldCloseOnTouch(getContext(), event)) {
            OtherUtils.INSTANCE.hideSoftInputFromWindow(et_input);
        }
        return super.onTouchEvent(event);
    }

    private boolean shouldCloseOnTouch(Context context, MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN
                && isOutOfBounds(context, event) && getWindow().peekDecorView() != null) {
            return true;
        }
        return false;
    }

    private boolean isOutOfBounds(Context context, MotionEvent event) {
        final int x = (int) event.getX();
        final int y = (int) event.getY();
        final int slop = ViewConfiguration.get(context).getScaledWindowTouchSlop();
        final View decorView = getWindow().getDecorView();
        return (x < -slop) || (y < -slop)
                || (x > (decorView.getWidth() + slop))
                || (y > (decorView.getHeight() + slop));
    }

    @Override
    public void show() {
        if (!isShowing()) {
            super.show();
            et_input.setText(draft);
            et_input.setSelection(et_input.getText().toString().length());
            updateView(InputMode.TEXT);
        }
    }

    //-------------custom-------------

    /**
     * 提示文字
     *
     * @param hint
     */
    public void setHint(CharSequence hint) {
        if (et_input != null) {
            et_input.setHint(hint);
        }
    }

    /**
     * 最大可输入汉字个数
     *
     * @param maxCount
     */
    public void setMaxCount(int maxCount) {
        this.maxCount = maxCount;
    }

    /**
     * 发送按钮回调监听
     *
     * @param onSendListener
     */
    public void setOnSendListener(OnSendListener onSendListener) {
        this.onSendListener = onSendListener;
    }
}