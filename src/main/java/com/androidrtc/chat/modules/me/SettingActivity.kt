package com.androidrtc.chat.modules.me

import ScoreDialogFragment
import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.CompoundButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.ToggleButton
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import com.androidrtc.chat.AppHelper
import com.androidrtc.chat.BuildConfig
import com.androidrtc.chat.CheckUpdateVersionHelper
import com.androidrtc.chat.R
import com.androidrtc.chat.UserHelper
import com.androidrtc.chat.customview.dialog.DebugSettingDialog
import com.androidrtc.chat.databinding.MeSettingActivityBinding
import com.androidrtc.chat.modules.me.auth.FaceAuthManager
import com.androidrtc.chat.modules.me.blacklist.BlackListActivity
import com.androidrtc.chat.modules.me.editprofile.EditProfileActivity
import com.androidrtc.chat.modules.me.teenager.TeenagerPwdActivity
import com.androidrtc.chat.modules.me.vip.setting.VipPrivilegeSettingActivity
import com.androidrtc.chat.modules.multiaccount.AccountSwitcherDialogFragment
import com.androidrtc.chat.modules.safety.AccountSecurityActivity
import com.androidrtc.chat.modules.safety.BindSecurityLevel
import com.androidrtc.chat.modules.safety.BindStateHelper
import com.androidtool.CommonApp
import com.androidtool.common.ToastUtils
import com.androidtool.common.activity.webview.H5
import com.androidtool.common.activity.webview.H5.url
import com.androidtool.common.base.BaseActivity
import com.androidtool.common.config.SystemSkinHelper
import com.androidtool.common.config.SystemSkinHelper.getSystemSkinByParam
import com.androidtool.common.config.SystemSkinHelper.systemSkinModeLiveData
import com.androidtool.common.constants.Constants
import com.androidtool.common.constants.FileDirs
import com.androidtool.common.data.PreferenceManager
import com.androidtool.common.extension.margin_top
import com.androidtool.common.extension.onClick
import com.androidtool.common.net.Apis
import com.androidtool.common.pop.PopLoading
import com.androidtool.common.provider.LoginApiProvider
import com.androidtool.common.provider.UserApiProvider
import com.androidtool.common.utils.FileUtils
import com.androidtool.common.utils.ImageLoader
import com.androidtool.common.utils.JSONUtil
import com.androidtool.common.utils.OtherUtils
import com.androidtool.common.utils.ScreenUtil.dp2px
import com.androidtool.common.utils.TranslateResource
import com.androidtool.common.utils.TranslateResource.getStringResources
import com.androidtool.common.utils.resToColor
import com.baitu.gift.util.FlowBus
import com.model.SoftVersion
import com.model.SystemSkinModel
import com.model.UserInfo
import com.poppo.exolibrary.util.cache.ProxyVideoCacheManager
import org.json.JSONObject

/**
 * me_setting_activity.xml
 * 设置页面
 */
class SettingActivity : BaseActivity(), View.OnClickListener {
    private val viewModel by viewModels<SettingViewModel>()

    private var needRefresh = true
    private var isFirstLoad = true
    private var clearing = false
    private lateinit var binding: MeSettingActivityBinding
    private var topBar: SystemSkinModel.TopBar? = null
    private var userApiProvider: UserApiProvider? = null
    private var loginApiProvider: LoginApiProvider? = null
        get() {
            if (userApiProvider == null) {
                field = LoginApiProvider(this)
            }
            return field
        }

    private fun getUserApiProvider(): UserApiProvider {
        if (userApiProvider == null) {
            userApiProvider = UserApiProvider(this)
        }
        return userApiProvider!!
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = MeSettingActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initView()
        initSystemSkinData()
        updateInfo()
        setGlobalSwitch()
    }

    @SuppressLint("CutPasteId")
    private fun initView() {
        binding.rlAbout.setOnClickListener(this)
        binding.newMessageSettings.setOnClickListener(this)
        binding.tvAboutTitle.text = getStringResources("about_poppo")
        binding.newMessageSettingsTitle.text = getStringResources("new_message_notification")
        binding.tvScoreTitle.text = getStringResources("rate_poppo")
        (findViewById<View>(R.id.title) as TextView).text =
            getStringResources(R.string.system_setting)
        binding.tvBlacklist.text = getStringResources(R.string.blacklist)
        binding.tvVersionTitle.text = getStringResources(R.string.me_version)
        binding.agreement1Text.text = getStringResources(R.string.privacy_policy)
        binding.agreement2Text.text = getStringResources(R.string.text_service_agreement)
        binding.closeAccountTitle.text = getStringResources("account_and_security")
        binding.privacySettingsTitle.text = getStringResources(R.string.me_private_setting)
        binding.clearCacheTitle.text = getStringResources(R.string.me_clear_storage)
        binding.tvLogoutLabel.text = getStringResources(R.string.logout)

        val rightImg = findViewById<ImageView>(R.id.topBarRightBtnImg)
        rightImg.setImageResource(R.drawable.icon_home_edit)
        rightImg.layoutParams = LinearLayout.LayoutParams(dp2px(44), dp2px(44))
        rightImg.setColorFilter(Color.parseColor("#666666"))
        findViewById<View>(R.id.backBtn).setOnClickListener(this)
        findViewById<View>(R.id.topBarRightBtn).setOnClickListener(this)
        findViewById<View>(R.id.topBarRightBtnImg).visibility = View.VISIBLE
        findViewById<View>(R.id.topBarRightBtnTxt).visibility = View.GONE
        // 接收群发
        binding.massMsgBtn.setOnClickListener(this)
        binding.locationBtn.setOnClickListener(this)
        binding.vipHiddenBtn.setOnClickListener(this)
        binding.autoReplyLayout.setOnClickListener(this)
        findViewById<View>(R.id.privacy_settings).setOnClickListener(this)
        findViewById<View>(R.id.agreement1).setOnClickListener(this)
        findViewById<View>(R.id.agreement2).setOnClickListener(this)
        findViewById<View>(R.id.clear_cache).setOnClickListener(this)
        findViewById<View>(R.id.close_account).setOnClickListener(this)
        findViewById<View>(R.id.blacklist_layout).setOnClickListener(this)
        findViewById<View>(R.id.changeLanguage).setOnClickListener(this)
        findViewById<View>(R.id.rlVersion).setOnClickListener(this)
        findViewById<View>(R.id.cl_switch_account).setOnClickListener(this)
        val tvChangeLanguage = findViewById<TextView>(R.id.tvChangeLanguage)
        tvChangeLanguage.text = getStringResources(R.string.select_language)
        val tvVersion = findViewById<TextView>(R.id.tvVersion)
        tvVersion.text = OtherUtils.getVersionName(this)
        findViewById<View>(R.id.logout_layout).setOnClickListener(this)
        // debug版本可见
        binding.debugSettingLayout.setOnClickListener(this)
        binding.debugSettingLayout.visibility = if (BuildConfig.DEBUG) View.VISIBLE else View.GONE
        val slotMachineSound = findViewById<ToggleButton>(R.id.slotMachineSound)
        slotMachineSound.isChecked = PreferenceManager.instance.slotMachineSound == 1
        slotMachineSound.setOnCheckedChangeListener { _: CompoundButton?, isChecked: Boolean ->
            PreferenceManager.instance.slotMachineSound = if (isChecked) 1 else 0
        }
        val limoSound = findViewById<ToggleButton>(R.id.limoSound)
        limoSound.isChecked = PreferenceManager.instance.limoSound == 1
        limoSound.setOnCheckedChangeListener { _: CompoundButton?, isChecked: Boolean ->
            PreferenceManager.instance.limoSound = if (isChecked) 1 else 0
        }
        val redPacketSound = findViewById<ToggleButton>(R.id.redPacketSound)
        redPacketSound.isChecked = PreferenceManager.instance.redPacketSound == 1
        redPacketSound.setOnCheckedChangeListener { _: CompoundButton?, isChecked: Boolean ->
            PreferenceManager.instance.redPacketSound = if (isChecked) 1 else 0
        }

        binding.teenagerToggle.setOnCheckedChangeListener { _: CompoundButton?, isChecked: Boolean ->
            val mIntent = Intent(this, TeenagerPwdActivity::class.java)
            if (isChecked) {
                if (TextUtils.isEmpty(PreferenceManager.instance.teenagerPwd)) {
                    mIntent.putExtra(TeenagerPwdActivity.TYPE, TeenagerPwdActivity.SET_PWD)
                } else {
                    mIntent.putExtra(
                        TeenagerPwdActivity.TYPE,
                        TeenagerPwdActivity.OPEN_TEENAGER_MODEL
                    )
                }
                startActivity(mIntent)
            }
        }
        binding.tvPrivilegeSettings.text = getStringResources("privilege_settings")
        systemSkinModeLiveData.observe(this, Observer { systemSkinModel: SystemSkinModel? ->
            if (systemSkinModel == null) {
                return@Observer
            }
            if (systemSkinModel.pages.setting != null) {
                val list = systemSkinModel.pages.setting!!.list
                if (list != null && list.isNotEmpty()) {
                    for (topBar in list) {
                        if (topBar.name == "privilege_settings") {
                            this.topBar = topBar
                            if (topBar.is_show == 1) {
                                binding.rlPrivilegeSettings.visibility = View.VISIBLE
                                binding.vPrivilegeSettingsLine.visibility = View.VISIBLE
                            } else {
                                binding.rlPrivilegeSettings.visibility = View.GONE
                                binding.vPrivilegeSettingsLine.visibility = View.GONE
                            }
                            break
                        }
                    }
                }
            }
        })
        binding.rlPrivilegeSettings.setOnClickListener {
            VipPrivilegeSettingActivity.jump(this)
        }

        binding.tvSafePsw.text = TranslateResource.getStringResources("setting_security_code")
        binding.tvClSwitchAccount.text = TranslateResource.getStringResources("switch_account")
    }

    private fun initSystemSkinData() {
        getSystemSkinByParam({ index: SystemSkinModel.Pages.Index? ->
            if (index == null) {
                return@getSystemSkinByParam
            }
            val list = index.list
            if (list != null && list.isNotEmpty() && this::binding.isInitialized) {
                for (topBar in list) {
                    if (topBar.name == "join_mcn") {
                        // 加入公会
                        //                        if (::binding.isLateinit) {
                        showTopBar(binding.joinMcn, binding.tvJoinMcn, topBar)
                        binding.joinMcn.setOnClickListener { v: View? ->
                            if (topBar.action != null) {
                                url(topBar.action!!.params.link)
                            }
                        }
                        //                        }
                    } else if (topBar.name == "about_us") {
                        // 关于我们
                        //                        if (::binding.isLateinit) {
                        showTopBar(binding.rlAbout, binding.tvAboutTitle, topBar)
                        binding.rlAbout.setOnClickListener { v: View? ->
                            if (topBar.action != null) {
                                url(topBar.action!!.params.link)
                            }
                        }
                        //                        }
                    } else if (topBar.name == "rate_poppo") {
                        // 给poppo评分
                        //                        if (::binding.isLateinit) {
                        binding.vScoreLine.visibility =
                            if (topBar.is_show == 1) View.VISIBLE else View.GONE
                        showTopBar(binding.rlScore, binding.tvScoreTitle, topBar)
                        binding.rlScore.setOnClickListener { v: View? ->
                            ScoreDialogFragment.newInstance().showContext(this@SettingActivity)
                        }
                        //                        }
                    } else if (topBar.name == "setting_security_code") {
                        // 安全密码
                        //                        if (::binding.isLateinit) {
                        binding.vSafePsw.visibility =
                            if (topBar.is_show == 1) View.VISIBLE else View.GONE
                        showTopBar(binding.rlSafePsw, binding.tvSafePsw, topBar)
                        binding.rlSafePsw.setOnClickListener { v: View? ->
                            if (topBar.action != null) {
                                FaceAuthManager.setFrom(FaceAuthManager.FROM_SETTING)
                                url(topBar.action!!.params.link)
                            }
                        }
                        //                        }
                    }
                }
            }
        }, SystemSkinHelper.SETTING)
    }

    private fun showTopBar(view: View?, textView: TextView?, topBar: SystemSkinModel.TopBar) {
        view!!.visibility =
            if (topBar.is_show == 1) View.VISIBLE else View.GONE
        if (topBar.style != null) {
            textView!!.text = topBar.style!!.label
        }
    }

    private fun setGlobalSwitch() {
        viewModel.getGlobalSwitch()
        viewModel.globalLiveData.observe(this) {
            if (it.first) {
                //显示开关
                binding.tvSwitchTitle.text = getStringResources("local_global_switch")
                binding.rlGlobalSwitch.isVisible = true
                binding.globalSwitchLine.isVisible = true
                binding.ivSwitchGlobal.isSelected = it.second
                if (it.second) {
                    binding.ivSwitchGlobal.setImageResource(R.drawable.icon_switch_on_blue)
                } else {
                    binding.ivSwitchGlobal.setImageResource(R.drawable.icon_switch_off)
                }
            } else {
                binding.rlVersion.margin_top = 8
                binding.rlGlobalSwitch.isVisible = false
                binding.globalSwitchLine.isVisible = false
            }
        }
        binding.rlGlobalSwitch.onClick {
            val selected = !binding.ivSwitchGlobal.isSelected
            binding.ivSwitchGlobal.isSelected = selected
            if (selected) {
                binding.ivSwitchGlobal.setImageResource(R.drawable.icon_switch_on_blue)
            } else {
                binding.ivSwitchGlobal.setImageResource(R.drawable.icon_switch_off)
            }
            viewModel.liveSwitchGlobal(selected)
            FlowBus.with<Boolean>(Constants.SWITCH_GLOBAL).emit(selected)
        }
    }

    override fun onResume() {
        super.onResume()
        binding.teenagerToggle.isChecked = PreferenceManager.instance.teenagerModeOpen
        getUserBind()
    }

    private fun initData() {
        if (isFirstLoad) {
            isFirstLoad = false
            PopLoading.setText(getStringResources(R.string.pop_loading)).show(this)
        }
        val params = "user_setting|is_show|vip_data"
        val paramMap = HashMap<String, String>()
        paramMap[Apis.INFOS] = params
        loginApiProvider?.getUserInfoByInfos(paramMap, { s: String? ->
            try {
                val jsonObject = JSONObject(s)
                PopLoading.hide()
                userInfo = JSONUtil.fromJSON(jsonObject.toString(), UserInfo::class.java)
                setData()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }) {
            PopLoading.hide()
        }
    }

    private fun setData() {
        if (userInfo.user_setting != null) {
            binding.massMsgBtn.isChecked = userInfo.user_setting!!.get_mass_msg > 0
            binding.locationBtn.isChecked = userInfo.user_setting!!.show_location > 0 // 显示地理位置
            binding.vipHiddenBtn.isChecked = userInfo.user_setting!!.stealth > 0 // vip隐身
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (needRefresh && hasFocus) {
            initData()
            needRefresh = false
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == RESULT_OK) {
            needRefresh = true
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.backBtn -> {
                finish()
            }

            R.id.topBarRightBtn -> {
                startActivity(Intent(this, EditProfileActivity::class.java))
                finish()
            }

            R.id.massMsgBtn -> save("get_mass_msg", binding.massMsgBtn.isChecked)
            R.id.vipHiddenBtn -> save("stealth", binding.vipHiddenBtn.isChecked)
            R.id.locationBtn -> {
                save("show_location", binding.locationBtn.isChecked)
                setResult(RESULT_OK)
            }

            R.id.blacklist_layout -> BlackListActivity.to(this)
            R.id.close_account -> startActivity(Intent(this, AccountSecurityActivity::class.java))
            R.id.clear_cache -> {
                if (clearing) {
                    ToastUtils.showToast(getStringResources(R.string.toast_is_clearing))
                    return
                }
                PopLoading.setText(getStringResources(R.string.toast_cleaning)).show(this)
                // 清除缓存小视频
                ProxyVideoCacheManager.clearAllCache(this)
                ImageLoader.clear(this)
                clearing = true
                Thread {
                    if (AppHelper.hasPermission(
                            this,
                            "android.permission.READ_EXTERNAL_STORAGE"
                        )
                    ) {
                        //                        File cacheDir = new File(AppHelper.getLBRootCachePathDir());
                        val cacheDir = FileDirs.getCacheDir()
                        FileUtils.deleteFileNoDirectory(cacheDir)
                    }
                    try {
                        Thread.sleep(100)
                    } catch (e: InterruptedException) {
                        e.printStackTrace()
                    }
                    clearing = false
                    runOnUiThread {
                        PopLoading.hide()
                        ToastUtils.showToast(getStringResources(R.string.toast_clean_finish))
                    }
                }.start()
            }

            R.id.privacy_settings -> startActivity(
                Intent(
                    this,
                    PrivacySettingsActivity::class.java
                )
            )

            R.id.agreement1 -> {
                H5.agreementPrivacy()
            }

            R.id.agreement2 -> {
                H5.agreement()
            }

            R.id.changeLanguage -> {
                startActivity(Intent(this, LanguageChangeActivity::class.java))
            }

            R.id.cl_switch_account -> {
                AccountSwitcherDialogFragment.show(
                    supportFragmentManager,
                    currentLoginUserId = PreferenceManager.instance.userId
                )
            }

            R.id.logout_layout -> {
                if (!this.isFinishing) {
                    UserHelper.logoutHint(this)
                }
            }

            R.id.debug_setting_layout -> {
                // debug版本可见
                if (BuildConfig.DEBUG) {
                    DebugSettingDialog(this).show()
                    CommonApp.testBridge?.showMsgFloat(this)
                }
            }

            R.id.auto_reply_layout -> {
                // 自动回复
                //                startActivity(Intent(this, AutoReplyConfigActivity::class.java))
            }

            R.id.rlVersion -> {
                PopLoading.setText(getStringResources(R.string.check_tips)).show(this)
                CheckUpdateVersionHelper.checkUpdateManual(this)
            }

            R.id.newMessageSettings -> startActivity(
                Intent(
                    this,
                    MessageSettingActivity::class.java
                )
            )

            else -> {}
        }
    }

    private fun clearTeenagerData() {
        PreferenceManager.instance.teenagerPwd = ""
        PreferenceManager.instance.teenagerModeDialog = true
        PreferenceManager.instance.teenagerModeOpen = false
    }

    private fun save(key: String, value: Boolean) {
        val map: MutableMap<String, String> = HashMap()
        map["key"] = key
        map["value"] = if (value) "1" else "0"
        getUserApiProvider().settingCreate(map, { s: String? ->
            try {
                UserHelper.initSettingInfo(this)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }) { }
    }

    private fun updateInfo() {
        val param = HashMap<String, String>()
        param["infos"] = "soft_version"
        param["manual"] = "1"
        loginApiProvider?.checkVersion(param, { versionData: SoftVersion? ->
            try {
                if (versionData != null && versionData.number > OtherUtils.getVersionCode(this@SettingActivity)) {
                    binding.ivNewVersion.visibility = View.VISIBLE
                } else {
                    binding.ivNewVersion.visibility = View.GONE
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }) { }
    }

    /**
     * 获取Google facebook绑定信息
     * 获取手机号和邮箱的绑定状态
     */
    private fun getUserBind() {
        PopLoading.show(this)
        getUserApiProvider().getBindInfo({
            try {
                val jsonObj = JSONObject(it)
                val phone = jsonObj.optString("phone")
                val email = jsonObj.optString("email")
                binding.vRedDot.visibility = View.GONE
                if (email.isNullOrEmpty()) {
                    if (PreferenceManager.instance.hasSetEmail) {
                        binding.vRedDot.visibility = View.GONE
                    } else {
                        binding.vRedDot.visibility = View.VISIBLE
                    }
                }

                when (BindStateHelper.getBindSecurityLevel(jsonObj)) {
                    BindSecurityLevel.LOW -> {
                        binding.tvSecurityLevel.setTextColor(R.color.red_text.resToColor())
                        binding.tvSecurityLevel.text =
                            "${getStringResources("level_safety")}：${getStringResources("safetylevel_low")}"
                    }

                    BindSecurityLevel.MIDDLE -> {
                        binding.tvSecurityLevel.setTextColor(R.color.yellow_text.resToColor())
                        binding.tvSecurityLevel.text =
                            "${getStringResources("level_safety")}：${getStringResources("safetylevel_middle")}"
                    }

                    BindSecurityLevel.HIGH -> {
                        // 高
                        binding.tvSecurityLevel.setTextColor(R.color.green_text.resToColor())
                        binding.tvSecurityLevel.text =
                            "${getStringResources("level_safety")}：${getStringResources("safetylevel_high")}"
                    }
                }

            } catch (e: Exception) {
                e.printStackTrace()
            }
            PopLoading.hide()
        }) {
        }
    }
}