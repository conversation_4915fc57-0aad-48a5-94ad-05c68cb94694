package com.androidrtc.chat.modules.me.teenager

import android.os.Bundle
import android.view.LayoutInflater
import androidx.core.view.isVisible
import com.androidrtc.chat.databinding.ActivityTeenagerPwdBinding
import com.androidtool.common.activity.webview.H5
import com.androidtool.common.extension.setTouchDelegate
import com.androidtool.common.utils.OtherUtils

class TeenagerPwdActivity : com.androidtool.common.base.BaseActivity() {
    companion object {
        const val TYPE = "type"
        const val OPEN_TEENAGER_MODEL = "open_teenager_model"
        const val EXIT_TEENAGER_MODEL = "exit_teenager_model"
        const val SET_PWD = "set_pwd"
        const val MODIFY = 0x00000001
    }

    private var currentState: State? = null
    lateinit var binding: ActivityTeenagerPwdBinding
    lateinit var setPwdState: SetPwdState
    lateinit var modifyPwdState: ModifyPwdState
    lateinit var confirmSetPwdState: ConfirmSetPwdState
    lateinit var openTeenagerState: OpenTeenagerState
    lateinit var exitTeenagerState: ExitTeenagerState
    var lastPwd = ""
    var status = 0x00000000

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTeenagerPwdBinding.inflate(LayoutInflater.from(this))
        setContentView(binding.root)
        initState()
        initView()
        initListener()
    }

    private fun initView() {
        initState(intent.getStringExtra(TYPE) ?: "")
        binding.back.setTouchDelegate(30)
    }

    private fun initState() {
        setPwdState = SetPwdState(this)
        modifyPwdState = ModifyPwdState(this)
        confirmSetPwdState = ConfirmSetPwdState(this)
        openTeenagerState = OpenTeenagerState(this)
        exitTeenagerState = ExitTeenagerState(this)
    }

    private fun initListener() {
        binding.back.setOnClickListener {
            finish()
        }

        binding.customerService.setOnClickListener {
            H5.customer(this)
        }

        binding.codeEditText.setOnCodeChangedListener(object : OnCodeChangedListener {
            override fun onVerCodeChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.btnConfirm.isEnabled = s?.length == 4
            }

            override fun onInputCompleted(s: CharSequence?) {
            }
        })

        binding.modifyPwd.setOnClickListener {
            status = status or MODIFY
            setState(modifyPwdState)
        }

        binding.btnConfirm.setOnClickListener {
            onButtonClick()
        }
    }

    private fun initState(type: String?) {
        when (type) {
            OPEN_TEENAGER_MODEL -> setState(openTeenagerState)
            EXIT_TEENAGER_MODEL -> setState(exitTeenagerState)
            SET_PWD -> setState(setPwdState)
        }
    }

    fun setState(state: State) {
        isShowModifyPwd(false)
        currentState = state
        currentState?.initInfo()
    }

    fun getInputCode() = binding.codeEditText.text.toString()

    fun isShowModifyPwd(isShow: Boolean) {
        binding.modifyPwd.isVisible = isShow
    }

    private fun onButtonClick() {
        currentState?.onButtonClick()
    }

    override fun onPause() {
        super.onPause()
        OtherUtils.hideSoftInputFromWindow(binding.codeEditText)
    }
}
