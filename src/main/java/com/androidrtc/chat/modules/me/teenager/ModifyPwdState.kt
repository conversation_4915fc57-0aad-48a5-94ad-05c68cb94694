package com.androidrtc.chat.modules.me.teenager

import com.androidrtc.chat.R
import com.androidtool.common.ToastUtils.showToast
import com.androidtool.common.data.PreferenceManager
import com.androidtool.common.utils.TranslateResource

class ModifyPwdState(private val activity: TeenagerPwdActivity) : State {
    override fun initInfo() {
        activity.binding.title.text = TranslateResource.getStringResources(R.string.modify_the_guardian_password)
        activity.binding.desc.text = TranslateResource.getStringResources(R.string.input_origin_pwd)
        activity.binding.btnConfirm.text = TranslateResource.getStringResources(R.string.next_step)
        activity.binding.codeEditText.setText("")
    }

    override fun onButtonClick() {
        if (activity.getInputCode() == PreferenceManager.instance.teenagerPwd) {
            activity.setState(activity.setPwdState)
        } else {
            showToast(TranslateResource.getStringResources(R.string.password_error))
        }
    }
}