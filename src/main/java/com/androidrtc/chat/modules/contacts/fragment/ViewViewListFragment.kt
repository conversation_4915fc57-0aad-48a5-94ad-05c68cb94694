package com.androidrtc.chat.modules.contacts.fragment

import com.androidtool.common.net.Apis
import com.androidtool.common.pop.PopLoading
import com.androidtool.common.utils.JSONUtil
import com.androidtool.common.widget.recyclerview2.LoadStatus
import com.google.gson.annotations.SerializedName
import com.model.BaseResponseMode
import com.model.UserInfo
import org.json.JSONException

class ViewViewListFragment : ViewListFragment() {

    override var isView: Boolean = true

    override fun loadDataFromServer(page: Int) {
        apiProvider.getUserInfoByInfos(
            mapOf(
                Pair(
                    Apis.INFOS,
                    Apis.getApiUserInfo("is_vip", "view_list")
                ), Pair("type", "view"),
                Pair("page", page.toString())
            ), {
                try {
                    PopLoading.hide()
                    val model = JSONUtil.fromJSON(it, Model::class.java)
                    if (model.list != null) {
                        setVip(model.isVip)
                        setListData(model.list)
                        errorView.hide()
                        if (model.list.isNullOrEmpty() || model.list.size < 15) {
                            setLoadStatus(LoadStatus.STATUS_NO_MORE_DATA)
                        } else {
                            setLoadStatus(LoadStatus.STATUS_NORMAL)
                        }
                    } else {
                        setLoadStatus(LoadStatus.STATUS_NORMAL)
                    }
                    if (page == 1 && model.list.isNullOrEmpty()) {
                        errorView.showEmptyView(true)
                    }

                } catch (e: JSONException) {
                    e.printStackTrace()
                }
            }) {
            PopLoading.hide()
            setLoadStatus(LoadStatus.STATUS_LOAD_FAILED)
        }
    }

    data class Model(
            @SerializedName("is_vip")
            val isVip: Int,
            @SerializedName("view_list")
            val list: ArrayList<UserInfo>?
    ) : BaseResponseMode()
}