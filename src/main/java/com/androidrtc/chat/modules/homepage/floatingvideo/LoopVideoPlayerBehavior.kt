package com.androidrtc.chat.modules.homepage.floatingvideo

import android.content.Context
import androidx.viewpager2.widget.ViewPager2
import com.model.SliderEventVideo

interface LoopVideoPlayerBehavior {
    fun initializeVideoPlayerBehavior(context: Context)
    fun setEventVideo(urls: List<SliderEventVideo.Slider>)
    fun setupVideoPlayer(container: ViewPager2)
    fun registerPageChangeListener(listener: OnPageChangeListener)
    fun isLooping(): Boolean
    fun startLoop()
    fun pauseLoop()
    fun releasePlayerResource()

    fun interface OnPageChangeListener {
        fun onChangePageItem(eventVideoSlider: SliderEventVideo.Slider)
    }
}


interface LivePlayerBehavior {
    fun initialize(context: Context)
    fun setupLivingPlayer(container: ViewPager2)
    fun playVideo()
    fun pauseVideo()
    fun setEventVideo(urls: List<SliderEventVideo.Slider>)
    fun isPlaying(): Boolean
    fun releasePlayerResource()
}
