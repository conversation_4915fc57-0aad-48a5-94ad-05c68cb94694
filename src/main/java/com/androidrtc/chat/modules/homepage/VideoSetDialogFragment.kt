package com.androidrtc.chat.modules.homepage

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.View
import com.androidrtc.chat.databinding.FragmentDialogVideoSetBinding
import com.androidtool.common.base.BaseBindingDialogFragment
import com.androidtool.common.extension.onClick
import com.androidtool.common.constants.BroadCastActions
import com.androidtool.common.provider.IndexDynamicApiProvider
import com.androidtool.common.utils.ScreenUtil
import com.androidtool.common.utils.TranslateResource
import com.model.Dynamic


/**
 *
 * @ProjectName:    submodulepoppo$
 * @Package:        com.androidrtc.chat.modules.homepage$
 * @ClassName:      RewardRecipientsDialogFragment
 * @Description:    精彩瞬间视频设置弹窗
 * @Author:         wangll
 * @CreateDate:     2024/3/12$ 10:08$
 * @UpdateUser:     更新者
 * @UpdateDate:     2024/3/12$ 10:08$
 * @UpdateRemark:   更新内容
 * @Version:        1.0
 */
class VideoSetDialogFragment : BaseBindingDialogFragment<FragmentDialogVideoSetBinding>() {
    override fun touchOutCancel(): Boolean {
        return true
    }

    override fun setDialogWidth(): Int {
        return ScreenUtil.getScreenWidth()
    }

    override fun setDialogHeight(): Int {
        return ScreenUtil.dp2px(360)
    }

    override fun setGravity(): Int {
        return Gravity.BOTTOM
    }

    override fun getAnimationStyle(): Int {
        return com.androidrtc.chat.livewidget.R.style.CommonBottomDialogAnim
    }

    override fun setTheme(): Int {
        return com.androidrtc.chat.livewidget.R.style.CommonBottomDialogStyle
    }

    override fun getBackgroundTransparency(): Float {
        return 0.1f
    }

    private var apiProvider: IndexDynamicApiProvider? = null
    private var dynamic: Dynamic? = null

    override fun initView() {
        binding.tvTitle.text = TranslateResource.getStringResources("setting_up")
        binding.tvContent.text = TranslateResource.getStringResources("video_pop_tip")
        binding.tvAllVisible.text = TranslateResource.getStringResources("video_pop_everyone")
        binding.tvGone.text = TranslateResource.getStringResources("video_pop_hide")
        binding.tvNext.text = TranslateResource.getStringResources("ok")
        binding.ivBack.onClick {
            if (isAdded) {
                dismissAllowingStateLoss()
            }
        }
        binding.tbtnAllVisible.onClick {
            binding.tbtnGone.isChecked = !binding.tbtnAllVisible.isChecked
        }
        binding.tbtnGone.onClick {
            binding.tbtnAllVisible.isChecked = !binding.tbtnGone.isChecked
        }

        binding.tvNext.onClick {
            dynamic?.run {
                val hidden = if (binding.tbtnAllVisible.isChecked) {
                    "0"
                } else {
                    "1"
                }
                val params = mutableMapOf(
                    Pair("id", id.toString()),
                    Pair("hidden", hidden)
                )
                apiProvider?.videoHide(params, {
                    dynamic?.hidden = hidden.toInt()
                    if (isAdded) {
                        dismissAllowingStateLoss()
                    }

                    context?.let { it1 ->
                        val intent = Intent(BroadCastActions.ACTION_CHANGE_VIDEO_SHOW_STATUS)
                        intent.setPackage(it1.packageName)
                        it1.sendBroadcast(intent)
                    }

                }, {})
            }

        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.let {
            dynamic = it.getSerializable(DYNAMIC) as Dynamic?
        }
        apiProvider = IndexDynamicApiProvider(activity)
        loadData()
    }


    @SuppressLint("SetTextI18n")
    private fun loadData() {
        dynamic?.run {
            binding.tbtnAllVisible.isChecked = hidden != 1
            binding.tbtnGone.isChecked = hidden == 1
        }
    }


    companion object {
        private const val DYNAMIC = "dynamic"

        @JvmStatic
        fun newInstance(
            dynamic: Dynamic
        ) = VideoSetDialogFragment().apply {
            arguments = Bundle().apply {
                putSerializable(DYNAMIC, dynamic)
            }
        }
    }


}