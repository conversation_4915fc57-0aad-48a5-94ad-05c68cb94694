package com.androidrtc.chat.modules.login.manager

import android.util.Log
import androidx.fragment.app.FragmentActivity
import com.androidrtc.chat.modules.login.TitTokAuthorize
import com.androidtool.common.ToastUtils
import com.androidtool.common.net.uploadlog.AVChatStatusLogHelper
import com.androidtool.common.pop.PopLoading
import com.androidtool.common.utils.JSONUtil.toJson
import com.tiktok.open.sdk.auth.AuthRequest
import com.tiktok.open.sdk.auth.AuthResponse
import com.tiktok.open.sdk.auth.utils.PKCEUtils

/**
 * author Dq
 * date on 2025/5/22
 * description TiKTok 三方登录
 */
class TikTokLoginManager {
    companion object {
        private const val TAG = "TikTokLoginManager"

        //开发者平台获取的 Client Key
        private const val CLIENT_KEY = "awsxeyek07f9lhki"

        //配置的回调URI,必须与开发者平台及 Manifest 中的配置完全一致
        private const val REDIRECT_URI = "https://poppolive.onelink.me/s4O6"
    }

    //TikTok授权帮助类
    private var authorize: TitTokAuthorize? = null

    /**
     * 发起 TikTok 登录授权请求。
     */
    fun authorize(
        activity: FragmentActivity,
        callback: (code: String, codeVerifier: String, redirectUri: String) -> Unit
    ) {
        PopLoading.show(activity)
        val codeVerifier = PKCEUtils.generateCodeVerifier()
        val request = AuthRequest(
            clientKey = CLIENT_KEY,
            scope = "user.info.basic",
            redirectUri = REDIRECT_URI,
            codeVerifier = codeVerifier
        )
        authorize = authorize ?: TitTokAuthorize()
        authorize?.startAuthorize(activity, request) {
            handleAuthResponse(it, codeVerifier, callback)
        }
    }

    /**
     * 处理从 TikTok 返回的授权响应 Intent。
     */
    private fun handleAuthResponse(
        response: AuthResponse?,
        codeVerifier: String,
        callback: (code: String, codeVerifier: String, redirectUri: String) -> Unit
    ) {
        if (response == null) {
            PopLoading.hide()
            AVChatStatusLogHelper.writerLog("TikTok 授权失败: response ==null")
            return
        }
        val code = response.authCode
        if (response.isSuccess && code.isNotEmpty()) {
            //TikTok 授权成功
            Log.e(TAG, "TikTok 授权成功code=$code")
            callback.invoke(code, codeVerifier, REDIRECT_URI)
        } else {
            //TikTok 授权失败
            PopLoading.hide()
            if (response.errorMsg != "access_denied") ToastUtils.showToast(response.errorMsg)
            AVChatStatusLogHelper.writerLog("TikTok 授权失败: ${response.toJson()}")
        }
    }
}