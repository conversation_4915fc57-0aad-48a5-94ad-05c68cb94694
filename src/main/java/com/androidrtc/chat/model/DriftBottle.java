/*
 * @Author: HePing <EMAIL>
 * @Copyright (c) 2017. Shanghai white rabbit Network Technology Co., Ltd. All rights reserved.
 */

package com.androidrtc.chat.model;

/**
 * 作者：Heping on 2017/9/19
 * 邮箱：<EMAIL>
 */
public class DriftBottle {

    /**
     * id : 16
     * content : 高规格
     * user : {"id":"22","nickname":"红红火火","avatar":"png_30834_937f93fef2329d394da8bef1b5d3ee69.png-cover2","sign":"","gender":"2","age":28,"last_online_at_text":"4分钟前","live_level":"17","vip_data":{"name":"未开通","level":"0","end_at":"0"},"wealth_level":"36"}
     */

    private String id;
    private String content;
    private UserBean user;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public UserBean getUser() {
        return user;
    }

    public void setUser(UserBean user) {
        this.user = user;
    }

    public static class UserBean {
        /**
         * id : 22
         * nickname : 红红火火
         * avatar : png_30834_937f93fef2329d394da8bef1b5d3ee69.png-cover2
         * sign :
         * gender : 2
         * age : 28
         * last_online_at_text : 4分钟前
         * live_level : 17
         * vip_data : {"name":"未开通","level":"0","end_at":"0"}
         * wealth_level : 36
         */

        private String id;
        private String nickname;
        private String avatar;
        private String sign;
        private String gender;
        private int age;
        private String last_online_at_text;
        private String live_level;
        private VipDataBean vip_data;
        private String wealth_level;
        private String is_online;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getSign() {
            return sign;
        }

        public void setSign(String sign) {
            this.sign = sign;
        }

        public String getGender() {
            return gender;
        }

        public void setGender(String gender) {
            this.gender = gender;
        }

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }

        public String getLast_online_at_text() {
            return last_online_at_text;
        }

        public void setLast_online_at_text(String last_online_at_text) {
            this.last_online_at_text = last_online_at_text;
        }

        public String getLive_level() {
            return live_level;
        }

        public void setLive_level(String live_level) {
            this.live_level = live_level;
        }

        public VipDataBean getVip_data() {
            return vip_data;
        }

        public void setVip_data(VipDataBean vip_data) {
            this.vip_data = vip_data;
        }

        public String getWealth_level() {
            return wealth_level;
        }

        public void setWealth_level(String wealth_level) {
            this.wealth_level = wealth_level;
        }

        public String getIs_online() {
            return is_online;
        }

        public void setIs_online(String is_online) {
            this.is_online = is_online;
        }

        public static class VipDataBean {
            /**
             * name : 未开通
             * level : 0
             * end_at : 0
             */

            private String name;
            private String level;
            private String end_at;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getLevel() {
                return level;
            }

            public void setLevel(String level) {
                this.level = level;
            }

            public String getEnd_at() {
                return end_at;
            }

            public void setEnd_at(String end_at) {
                this.end_at = end_at;
            }
        }
    }
}
