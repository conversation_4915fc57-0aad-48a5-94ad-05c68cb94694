package com.androidrtc.chat

import com.androidrtc.im.open.provider.ui.IMUIProvider

/**
 * @author: mao.ye
 * @updateTime: 2024-09-03
 * @description:IM模块UI提供者
 */
class IMUIProviderImpl : IMUIProvider {
    override fun themeColor(): Int {
        return R.color.main_color_purple
    }

    override fun chatBackground(): Int {
        return R.color.windowBackground
    }

    override fun chatLeftBubbleBubblesBackground(): Int {
        return R.drawable.bg_chat_bubbles_other
    }

    override fun chatRightBubbleBubblesBackground(): Int {
        return R.drawable.bg_chat_bubbles_my
    }

    override fun chatWithdrawalButtonBackground(): Int {
        return R.drawable.selector_btn_blue_5259f7_r100
    }

    override fun chatSendButtonBackground(): Int {
        return R.drawable.selector_btn_blue_5259f7_r100
    }

    override fun chatLeftContentTextColor(): Int {
        return R.color.gray_1
    }

    override fun chatRightContentTextColor(): Int {
        return R.color.white
    }

    override fun getLoadingPopColoursRes(): Int {
        return R.drawable.webp_loading_colours
    }

    override fun getLoadingPopGrayRes(): Int {
        return R.drawable.webp_loading_gray

    }

    override fun getDefaultLoadImg(): Int {
        return R.drawable.shape_image_default

    }
}