package com.model

/**
 * @author: mao.ye
 * @updateTime: 2024-06-04
 * @description:分享类型
 */
object ShareType {
    const val ROOM = "room"//直播间分享
    const val VIDEO_ACTIVITY = "video-activity"//视频活动分享
    const val DYNAMIC = "dynamic"//动态分享
    const val TOPIC = "topic"//话题分享
    const val INVITE_GIRL_REGISTER = "InviteGirlRegister"//新用户注册
    const val INVITE_BOY_REGISTER = "InviteBoyRegister"//新用户注册
    const val NORMAL = "normal"//普通通用分享

}