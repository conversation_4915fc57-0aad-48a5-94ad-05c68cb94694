package com.model


import com.google.gson.annotations.JsonAdapter
import com.google.gson.annotations.SerializedName
import com.androidtool.common.json.RawStringJsonAdapter

data class AwardModel(
    @SerializedName("game_icon")
    val gameIcon: String = "",
    @SerializedName("game_name")
    val gameName: String = "",
    @SerializedName("prize_icon")
    val prizeIcon: String = "",
    @SerializedName("is_random")
    val isRandom: String = "",
    @SerializedName("user_vip_data")
    val userVipData: UserVipData?=null,
    @SerializedName("created_at")
    val createdAt: String = "",
    @SerializedName("user_gender")
    val userGender: String = "",
    @SerializedName("user_wealth_level")
    val userWealthLevel: String = "",
    @SerializedName("is_room")
    val isRoom: String = "",
    @SerializedName("to_user_gender")
    val toUserGender: String = "",
    @SerializedName("duration")
    val duration: String = "",
    @SerializedName("uid")
    val uid: String = "",
    @SerializedName("gift_log_id")
    val giftLogId: String = "",
    @SerializedName("anonymity")
    val anonymity: Anonymity?,
    @SerializedName("rate")
    val rate: String = "",
    @SerializedName("hali_bonus")
    val haliBonus: Int =0,
    @SerializedName("hali_effect")
    val haliEffect: String ?=null,
    @SerializedName("draw_effect")
    val drawEffect: String = "",
    @SerializedName("user_nickname")
    val userNickname: String = "",
    @SerializedName("user_live_level")
    val userLiveLevel: String = "",
    @SerializedName("id")
    val id: String = "",
    @SerializedName("text")
    val text: String = "",
    @SerializedName("to_user_nickname")
    val toUserNickname: String = "",
    @SerializedName("user_avatar")
    val userAvatar: String = "",
    @SerializedName("user_coins")
    val userCoins: String = "",
    @SerializedName("effect_audio")
    val effectAudio: String = "",
    @SerializedName("gift_filename")
    val giftFilename: String = "",
    @SerializedName("show_text")
    val showText: Int = 0,
    @SerializedName("hide_chat")
    val hideChat: Int = 0,
    @SerializedName("like_count")
    val likeCount: String = "",
    @SerializedName("start_id")
    val startId: String = "",
    @SerializedName("gift_id")
    val giftId: String = "",
    @SerializedName("msg_text")
    val msgText: String = "",
    @SerializedName("win_coin")
    val winCoin: String ?= null,
    @SerializedName("display_text_color")
    val displayTextColor: String = "gold",  //默认展示金色
    @SerializedName("all_draw_effect")
    val allDrawEffect: String = "",
    @SerializedName("created_by")
    val createdBy: String = "",
    @SerializedName("room_id")
    val roomid: String = "",
    @SerializedName("draw_id")
    val drawId: String = "",
    @SerializedName("enable_at")
    val enableAt: String = "",
    @SerializedName("to_uid")
    val toUid: String = "",
    @SerializedName("updated_by")
    val updatedBy: String = "",
    @SerializedName("gift_name")
    val giftName: String = "",
    @SerializedName("coin")
    val coin: String = "",
    @SerializedName("content")
    val content: String,
    @JsonAdapter(RawStringJsonAdapter::class)
    @SerializedName("custom")
    val custom: String = ""
)