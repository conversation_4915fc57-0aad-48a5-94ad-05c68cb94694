package com.baitu.debug.ui.translate

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.androidtool.common.utils.TranslateResource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * @author: mao.ye
 * @updateTime: 2025-05-27
 * @description:
 */
class FindTranslateViewModel : ViewModel() {
    val translateLiveData by lazy { MutableLiveData<ArrayList<TranslateInfo>>() }

    /**
     * 查找翻译
     */
    fun findTranslate(key: String) {
        if (key.isEmpty()) {
            getTranslateList()
            return
        }
        viewModelScope.launch(Dispatchers.Default) {
            val map = TranslateResource.getAllCompleteMap()
            val list = ArrayList<TranslateInfo>()
            map.forEach {
                if (it.key.contains(key)) {
                    list.add(TranslateInfo(it.key, it.value))
                } else if (it.value.contains(key)) {
                    list.add(TranslateInfo(it.key, it.value))
                }
            }
            viewModelScope.launch(Dispatchers.Main) {
                translateLiveData.value = list
            }
        }
    }

    /**
     * 获取所有的翻译列表
     */
    fun getTranslateList() {
        viewModelScope.launch(Dispatchers.Default) {
            val map = TranslateResource.getAllCompleteMap()
            val list = ArrayList<TranslateInfo>()
            map.forEach {
                list.add(TranslateInfo(it.key, it.value))
            }
            viewModelScope.launch(Dispatchers.Main) {
                translateLiveData.value = list
            }
        }
    }
}