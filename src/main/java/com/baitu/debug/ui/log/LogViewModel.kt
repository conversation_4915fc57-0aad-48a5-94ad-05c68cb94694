package com.baitu.debug.ui.log

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.baitu.debug.ui.log.repository.CacheRepository
import com.baitu.debug.ui.log.repository.LogRepository
import com.baitu.debug.ui.log.repository.RequestLogRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * @author: mao.ye
 * @updateTime: 2024-10-24
 * @description:日志页面viewmodel
 */
class LogViewModel : ViewModel() {
    val logTypeLiveData by lazy {
        MutableLiveData<Int>()
    }
    val logLiveData by lazy {
        MutableLiveData<ArrayList<LogItem>>()
    }
    private var working = false
    private var logRepository = LogRepository()
    private var requestLogRepository = RequestLogRepository()
    private var cacheRepository = CacheRepository()

    /**
     * 按类型获取日志列表
     */
    fun getLogList(type: Int, page: Int, findKeyword: String? = null) {
        if (working) {
            return
        }
        working = true
        viewModelScope.launch(Dispatchers.IO) {
            val data = if (type != 8) logRepository.getLogList(
                type,
                page
            ) else requestLogRepository.getLogList(page)
            if (findKeyword.isNullOrEmpty()) {
                withContext(Dispatchers.Main) {
                    logLiveData.value = data
                    working = false
                }
            } else {
                val filterData = arrayListOf<LogViewModel.LogItem>()
                for (item in data) {
                    if (item.log.contains(findKeyword)) {
                        filterData.add(item)
                    }
                }
                withContext(Dispatchers.Main) {
                    logLiveData.value = filterData
                    working = false
                }
            }
        }
    }

    /**
     * 获取所有的日志类型
     */
    fun getLogType(): ArrayList<LogItem> {
        return arrayListOf(
            LogItem(0, "全部"),
            LogItem(8, "接口请求"),
            LogItem(1, "AVCHAT"),
            LogItem(2, "直播间"),
            LogItem(3, "Crash"),
            LogItem(4, "IM"),
            LogItem(5, "MQTT消息"),
            LogItem(6, "美颜"),
            LogItem(7, "设备信息"),
        )
    }

    /**
     * 按关键字查找日志
     * return 日志位置position集合
     */
    fun findLog(data: List<LogItem>, log: String): ArrayList<Int> {
        if (log.isEmpty()) {
            return arrayListOf()
        }
        val pointRange = arrayListOf<Int>()
        for (i in data.indices) {
            if (data[i].log.contains(log)) {
                pointRange.add(i)
            }
        }
        return pointRange
    }

    fun saveLogType(type: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            cacheRepository.cacheLogType(type.toString())
        }
    }

    fun loadCacheType() {
        viewModelScope.launch(Dispatchers.IO) { //加载缓存的type
            val type = cacheRepository.getLogType()
            val typeList = getLogType()
            val index = typeList.indexOfFirst { it.type == type.toIntOrNull() }
            withContext(Dispatchers.Main) {
                if (index != -1) {
                    logTypeLiveData.value = index
                } else {
                    logTypeLiveData.value = 0
                }
            }

        }
    }

    data class LogItem(val type: Int, val log: String)
}