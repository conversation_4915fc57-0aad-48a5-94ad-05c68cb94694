package com.androidtool.common.troll

import com.google.gson.Gson
import okhttp3.MediaType
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okio.BufferedSink
import okio.GzipSink
import okio.buffer
import java.io.IOException


object RequestBodyUtils {
    /**
     * Method for building request body for post requests in JSON style
     */
    fun buildJSONRequestBody(obj: Any): RequestBody {
        var jsonStr = ""
        try {
            jsonStr = if (obj is String) obj else Gson().toJson(obj)
        } catch (ignored: Exception) {
        }
        return jsonStr.toRequestBody(BodyMediaType.JSON)
    }

    /**
     * Transfer a body to gzip style.
     */
    fun buildGzipBody(body: RequestBody, gzip: Boolean): RequestBody {
        val type = body.contentType()
        return object : RequestBody() {
            override fun contentType(): MediaType? {
                return type
            }

            @Throws(IOException::class)
            override fun contentLength(): Long {
                return -1
            }

            @Throws(IOException::class)
            override fun writeTo(sink: BufferedSink) {
                val gzipSink: BufferedSink = if (gzip) GzipSink(sink).buffer() else sink
                body.writeTo(gzipSink)
                gzipSink.close()
            }
        }
    }

}