package com.androidtool.common.troll.service


import android.R.attr.tag
import com.androidtool.common.troll.BaseHttpHelper
import com.androidtool.common.troll.intercepter.BTHeaderParamInterceptor
import com.androidtool.common.troll.intercepter.DTExtraParamInterceptor
import com.androidtool.common.troll.intercepter.OkMonitorInterceptor
import com.androidtool.common.troll.singleton.LazySingletonProvider
import com.androidtool.common.troll.singleton.OkHttpClientSingleton
import com.androidtool.common.troll.util.CustomHttpLoggingInterceptor
import com.androidtool.common.troll.util.ReleaseHttpLogger
import com.rtc.request.RtcRequestHelper
import okhttp3.ConnectionPool
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.util.concurrent.TimeUnit


class OkHttpClientSingletonService {
    companion object {
        var defaultOkHttpProvider: LazySingletonProvider<OkHttpClient> =
            object : LazySingletonProvider<OkHttpClient>() {
                override fun createInstance(type: Int): OkHttpClient {
                    return BaseHttpHelper.getInstance().getOkHttpClient(type).newBuilder()
                        .connectionPool(ConnectionPool(32, 5, TimeUnit.MINUTES))
                        .connectTimeout(30, TimeUnit.SECONDS) // 连接超时 30s
                        .addInterceptor(DTExtraParamInterceptor())
                        .addInterceptor(OkMonitorInterceptor())
                        .apply {
                            if (RtcRequestHelper.isDebug) {
                                addInterceptor(CustomHttpLoggingInterceptor())
                            } else {
                                addInterceptor(HttpLoggingInterceptor(ReleaseHttpLogger()).apply {
                                    level = HttpLoggingInterceptor.Level.BODY
                                })
                            }
                        }.addInterceptor(BTHeaderParamInterceptor()).build()
                }
            }

        fun init() {
            OkHttpClientSingleton.setDefaultOkHttpProvider(defaultOkHttpProvider)
            OkHttpClientSingleton.setApiOkHttpProvider(defaultOkHttpProvider)
        }
    }
}