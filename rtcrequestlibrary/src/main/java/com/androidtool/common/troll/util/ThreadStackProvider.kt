package com.androidtool.common.troll.util

import android.os.Looper

// 定义一个接口来获取主线程调用栈
interface ThreadStackProvider {
    fun getMainThreadStack(): String?
    fun getNetworkThreadStack(): String?
}

// 实现一个默认的提供者
class DefaultThreadStackProvider : ThreadStackProvider {
    override fun getMainThreadStack(): String? {
        return try { // 获取主线程的调用栈
            Looper.getMainLooper().thread.stackTrace.filter { shouldOutputStackTrace(it.className) }.take(20)
                .mapIndexed { index, element ->
                    formatStackElement(index + 1, element)
                }.joinToString("\n")
        } catch (e: Exception) {
            null
        }
    }

    override fun getNetworkThreadStack(): String? {
        return try {
            val networkThreadStack =
                Thread.currentThread().stackTrace.dropWhile { shouldOutputStackTrace(it.className) }.take(20)
                    .mapIndexed { index, element ->
                        formatStackElement(index + 1, element)
                    }.joinToString("\n")
            networkThreadStack
        } catch (e: Exception) {
            null
        }
    }


    private fun shouldIgnoreStackTrace(className: String): Boolean {
        return className.startsWith("okhttp3") ||
                className.startsWith("java.lang") ||
                className.startsWith("dalvik.system") ||
                className.contains("RequestStackTracker")
    }

    private fun shouldOutputStackTrace(className: String): Boolean {
        return when {
            className.contains("Activity") -> true
            className.contains("Fragment") -> true
            className.contains("Dialog") -> true
            className.contains("ViewModel") -> true
            className.contains("ViewBinding") -> true
            className.contains("Repository") -> true
            className.contains("DataSource") -> true
            className.contains("UseCase") -> true
            className.contains("Service") -> true
            className.contains("Manager") -> true
            className.contains(".ui.") -> true
            className.contains(".page.") -> true
            className.contains(".feature.") -> true
            className.contains(".business.") -> true
            className.contains(".module.") -> true
            className.contains("Helper") -> true
            // 协程相关
            className.contains("CoroutineScope") -> true
            className.contains("Dispatchers") -> true
            // RxJava 相关 (如果项目使用)
            className.contains("Observable") -> true
            className.contains("Subscriber") -> true
            className.contains("Observer") -> true
            else -> false
        }
    }

    private fun formatStackElement(index: Int, element: StackTraceElement): String {
        val fileName = element.fileName ?: "Unknown"
        val lineNumber = element.lineNumber.takeIf { it >= 0 } ?: "Unknown"
        val simpleClassName = element.className.substringAfterLast('.')
        return "├─ ($index) ($fileName:$lineNumber) ${element.className}.${element.methodName}"
    }
}