apply plugin: 'maven-publish'
def GroupId = 'rtc'
def releasesRepoUrl = "${maven_path}"
def Version = "${libs.versions.liveWidget.get()}"
def ArtifactId = 'live_widget'
def artifactPath = "aar/rtc-live-widget-${libs.versions.liveWidget.get()}.aar"

project.afterEvaluate {
    publishing {
        //发布的 arr 包配置
        publications {
            //名字可以自己指定，如果有多渠道，整段多复制一个
            release(MavenPublication) {
                groupId = GroupId//公司域名
                artifactId = ArtifactId//该aar包的名称
                version = Version//版本号
                artifact artifactPath
                //依赖关系
                pom.withXml {
                    def dependenciesNode = asNode().appendNode("dependencies")
                    configurations.implementation.allDependencies.forEach() {
                        Dependency dependency ->
                            if (dependency.version != "unspecified" && dependency.name != "unspecified") {
                                def dependencyNode = dependenciesNode.appendNode('dependency')
                                dependencyNode.appendNode('groupId', dependency.group)
                                dependencyNode.appendNode('artifactId', dependency.name)
                                dependencyNode.appendNode('version', dependency.version)
                            }
                    }
                }
            }
        }


        //仓库地址配置
        repositories {
            maven {
                url = releasesRepoUrl
                credentials(HttpHeaderCredentials) {
                    name = 'Private-Token'
                    value = "${maven_access_token}"
                }
                authentication {
                    header(HttpHeaderAuthentication)
                }
            }
        }
    }

}


